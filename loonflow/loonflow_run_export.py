#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

'''
fusion虚机迁移-镜像导出
调用fusion虚机模板导出接口

'''

from workflow.service.ticket.ticket_base_service import TicketBaseService
import paramiko
import json
import requests
import time
import re


# ERP账号密码
yiyunwei_url = "http://aioms.caih.local"
username = "******"
password = "******"
# ckv节点dapp密码
ckv_node = "" 
ckv_node_password = "******"
# 中转nfs虚机root密码
trans_vm = ""      # 从工单获取
trans_vm_password = "******"
# 华为云用户密码
fusion_addr = ""   # 从工单获取  https://**********:8443/ 或https://***********:8443/
fusion_authuser = "vmexport"
fusion_authkey = "******"



fusion_platform = {
    "https://**********:8443/": {    # 南宁      
        "flag": "4AF909D7",
        "platform_name": "华为云-南宁电信2",
        "pool": "telecom2"
    },
    "https://***********:8443/": {  # 北京
        "flag": "380C0718",
        "platform_name": "华为云-北京",
        "pool": "vianet"
    }
}


def run():
    global fusion_addr
    # 获取工单信息ip地址信息
    # ticket_id会通过exec传过来
    # 你也可以获取工单的其他字段信息，包括自定义字段的值。根据获取的值做后续处理
    trans_vm, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'trans_vm')
    vm_list, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'delvm_list')
    fusion_addr, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'fusion_addr')
    if fusion_addr not in fusion_platform.keys():
        print(f'[失败]请检查华为云地址{fusion_addr}是否正确')
        TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: fusion_platform invalid"})
        return
    fusion_flag = fusion_platform[fusion_addr].get("flag")

    vm_info = json.loads(vm_list)
    for n in vm_info:
        vm_ip = n.get('ip')
        vm_id = n.get('vm_id')
        for item in n.get('attribute_value'):
            if item.get('attr').get('attr_name') == 'VM_ID':
                vm_id = item.get('stringvalue')
    if vm_id == None or vm_id == '':
        print("get vm_id failed")
        TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: get vm_id failed"})
        return
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        timestamp = int(time.time())
        TicketBaseService.update_ticket_field_value(ticket_id, {"start_time":timestamp})
        ssh.connect(hostname=trans_vm, username='root', port=22, password=trans_vm_password, timeout=10)
        # 如果导出目录已存在则直接返回成功
        cmdStr = "ls /app/nfs/{}/{}.ovf".format(vm_ip,vm_ip)
        _, stdout, _ = ssh.exec_command(cmdStr)
        returncode = stdout.channel.recv_exit_status()
        if returncode == 0:  # ovf存在则直接跳过
            print("/app/nfs/{}/{}.ovf已存在,跳过导出,如需重新导出请删除目录".format(vm_ip,vm_ip)) 
            TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"进行中: ovf已存在, 跳过"})
            return

        # 登录
        # 南宁电信
        session = requests.session()
        # print(fusion_addr)
        url =f'{fusion_addr}service/login/form'
        data = {"acceptLanguage": "zh-CN",
        "authKey": fusion_authkey,
        "authType": "0",
        "authUser": fusion_authuser,
        "userType": "0",
        "verification": ""}
        session.headers["Content-Type"]="application/json"
        res = session.post(url, data=json.dumps(data), verify=False, timeout=10)
        session.headers['CSRF-HW'] = res.json()['csrfToken']

        print("开始执行导出")
        url = f'{fusion_addr}service/sites/{fusion_flag}/vms/{vm_id}/action/export'
        data = {"protocol":"nfs","name":vm_ip,"url":"{}:/app/nfs/{}".format(trans_vm, vm_ip),"format":"ovf","isOverwrite":True}
        res = session.post(url, data=json.dumps(data), verify=False)
        if res.status_code != 200:
            print("接口调用失败")
            TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: 接口调用失败"})
            return
        elif res.json().get('errorDes') != None:
            print("接口报错: ", res.json().get('errorDes'))
            TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: {}".format(res.json().get('errorDes'))})
            return
        else:
            taskUri = res.json().get('taskUri')
            if taskUri == None:
                print("失败: get taskUri invalid")
                TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: get taskUri invalid"})
                return
            taskid = taskUri.split("tasks/")[-1]    # "/service/sites/4AF909D7/tasks/1931915"
            TicketBaseService.update_ticket_field_value(ticket_id, {"taskid":taskid})
            TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"进行中: 执行ok"})
    except Exception as e:
        print("执行异常: ", str(e))
        # traceback.print_exc()
        TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: {}".format(str(e))})
    finally:
        ssh.close()
        session.close()

run()
