# -*- coding: utf-8 -*-


import requests, json
from requests.packages.urllib3.exceptions import InsecureRequestWarning

'''
- 获取正在使用的虚机总数
- 获取华为云(使用中)虚机个数
- 获取容器云(使用中)虚机个数
- 获取未计费的虚机
'''


requests.packages.urllib3.disable_warnings(InsecureRequestWarning)  # 屏蔽访问过程中的warning信息

# 易运维登陆
username = "XXXXXX"
password = "XXXXXX"
yyw_url = 'http://10.25.1.25'

def main():
    not_cost_vm_list = []
    hauwei_nn_dianxin1 = 0
    hauwei_nn_dianxin2 = 0
    huawei_nn_dianxin_arm = 0
    huawei_shijiazhuang_cnt = 0
    hauwei_bj_cnt = 0
    chaoronghe_vm_cnt = 0
    test_huasan_vm_cnt = 0
    ckv_vm_test_cnt = 0
    ckv_vm_pro_cnt = 0
    ckv_vm_bjpro_cnt = 0
    other_vm_cnt = 0
    empty_platform_vm_list = []
    session = requests.session()
    session.headers['Content-Type'] = 'application/json'
    res = session.post(f'{yyw_url}/token/', data=json.dumps({"username": username, "password": password}))

    try:
        if res.status_code != 200:
            print("认证失败：{}".format(res.status_code))
            exit(1)
        token = res.json()['access']
    except Exception as e:
        print(str(e))
        print('认证异常...')
        exit(1)  
    session.headers['Authorization'] = f'Bearer {token}'

    res = session.get(f'{yyw_url}/api/hosts/?limit=3000&offset=0&asset_type=Virtual+machine&status=1')  # 查询所有使用中的主机
    all_vm_count = len(res.json()['results'])
    print(f'当前易运维所有正在使用的虚机数: {all_vm_count}')
    

    for host in res.json()['results']:
        for _attr in host['attribute_value']:
            if _attr['attr']['attr_name'] == 'IS_COST':
                if _attr["stringvalue"] != "true":
                    not_cost_vm_list.append(host['hostname'])
        if host['cloud_platform'] == None:
            empty_platform_vm_list.append(host['ip'])
            continue
        platform_name = host['cloud_platform']['platform_name']
        if platform_name == "华为云-南宁电信1":
            hauwei_nn_dianxin1 += 1
        elif platform_name == "华为云-南宁电信2":
            hauwei_nn_dianxin2 += 1
        elif platform_name == "华为云-北京":
            hauwei_bj_cnt += 1
        elif platform_name == "华为云-南宁电信ARM":
            huawei_nn_dianxin_arm += 1
        elif platform_name == "华为云-石家庄联通":
            huawei_shijiazhuang_cnt += 1
        elif platform_name == "超融合云平台":
            chaoronghe_vm_cnt += 1
        elif platform_name == "测试华三云":
            test_huasan_vm_cnt += 1
        elif platform_name == "容器云虚拟化(测试)-南宁电信":
            ckv_vm_test_cnt += 1
        elif platform_name == "容器云虚拟化(生产)-南宁电信":
            ckv_vm_pro_cnt += 1
        elif platform_name == "北京机房容器虚拟化集群":
            ckv_vm_bjpro_cnt += 1
        else:
            other_vm_cnt += 1
            print(f'未识别的平台虚机: {platform_name}/{host["ip"]}')
        
        for _attr in host['attribute_value']:
            if _attr['attr']['attr_name'] == 'IS_COST':
                if _attr["stringvalue"] != "true":
                    not_cost_vm_list.append(host['hostname'])

    print(f'华为云-南宁电信1虚机总数: {hauwei_nn_dianxin1}')
    print(f'华为云-南宁电信2虚机总数: {hauwei_nn_dianxin2}')
    print(f'华为云-北京虚机总数: {hauwei_bj_cnt}')
    print(f'华为云-南宁电信ARM虚机总数: {huawei_nn_dianxin_arm}')
    print(f'华为云-石家庄联通虚机总数: {huawei_shijiazhuang_cnt}')
    print(f'超融合云平台虚机总数: {chaoronghe_vm_cnt}')
    print(f'测试华三云虚机总数: {test_huasan_vm_cnt}')
    print(f'容器云测试集群虚机数: {ckv_vm_test_cnt}')
    print(f'容器云生产集群虚机数: {ckv_vm_pro_cnt}')
    print(f'北京机房容器虚拟化集群虚机数: {ckv_vm_bjpro_cnt}')
    print(f'未识别的平台虚机总数: {other_vm_cnt}')
    print(f'平台未填写的虚机数: {len(empty_platform_vm_list)}')
    print(f'当前未计费的虚机总数: {len(not_cost_vm_list)}')

    
    # 获取未计费的虚机
    # print('未计费的虚机列表：')
    # for vm in not_cost_vm_list:
    #     print(vm)
 

if __name__ == "__main__":
    main()

