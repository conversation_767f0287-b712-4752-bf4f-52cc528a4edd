#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

import requests
import json
import ssl
from requests.packages.urllib3.exceptions import InsecureRequestWarning


'''
@ 需求1: 遍历所有由csi创建的且relocation_policy(DATATRANSFERPOLICY)不等于automatic(自动迁移)的lun
* 如果不为自动迁移，修改为自动迁移

@ 需求2: 只针对北京5610: 
* 如果SMARTCACHESTATE不为“1”,添加smartcache

@ 其他：
* csi创建的lun的DESCRIPTION默认是:Created from Kubernetes CSI
* 'ks8-'开头的lun不需要设置为自动迁移
* 增加黑名单处理(跳过黑名单lun)
* 适配南宁/北京东海存储5610 5310

@ 执行:
  验证环境: ************
  /home/<USER>/hff/python
  python3.6 fix_huawei_lun.py
'''


oceanstore_platforms = {
    "oceanstore5610-nn": {
        'url': 'https://************:8088',
        'user': 'sysuser02',
        'password': 'XXXXX'
    },
    "oceanstore5310-nn": {
        'url': 'https://************:8088',
        'user': 'sysuser02',
        'password': 'XXXXX'
    },
     "oceanstore5610-bj": {  # 北京5610
        'url': 'https://************:8088',
        'user': 'sysuser02',
        'password': 'XXXXX'
    }
}


login_deviceId = None
login_iBaseToken = None
login_cookies = None


blacklist = [
    'pvc-595fbf06-2e79-453b-b347-eda', # 陆砚华测试高性能
    'pvc-30de001b-fba4-41f2-9101-988',  # 陆砚华测试高性能
    'pvc-4f8781d8-6f5a-4c19-abb5-f17' # 陆砚华测试高性能
]


# 登录https站点时不弹出警告信息
ssl._create_default_https_context = ssl._create_unverified_context
requests.packages.urllib3.disable_warnings(
    InsecureRequestWarning)  # 屏蔽访问过程中的warning信息

def login(url, username, passwd, session):
    global login_deviceId, login_iBaseToken, login_cookies
    login_deviceId = None
    login_iBaseToken = None
    login_cookies = None

    url_session = url + '/deviceManager/rest/xxxxx/sessions'
    body = {
        'username': username,
        'password': passwd,
        'scope': '0'
    }
    resp = session.post(url_session, data=json.dumps(body), verify=False)
    if resp.json().get('error')['code'] != 0:
        print('登录失败: {}'.format(resp.json().get('error')['code']))
        resp = session.post(url_session, data=json.dumps(body), verify=False)
    res_data = json.loads(resp.text)['data']
    login_deviceId = res_data.get('deviceid', '')
    login_iBaseToken = res_data.get('iBaseToken', '')
    login_cookies = resp.cookies
    if not login_deviceId:
        print(f'[ERROR] 获取deviceId失败')
        # exit(22) # 脚本里不能exit非0，否则工单会异常
    # return deviceId, session, iBaseToken, cookies


def fix_huawei_lun(platform_name, oceanstore_url, oceanstore_user, oceanstore_password):
    global login_deviceId, login_iBaseToken, login_cookies

    try:
        tofix_automatic_luns = []
        tofix_smartcache_lunns = []
        # other_not_automatic_luns = []
        session = requests.Session()
        login(oceanstore_url, oceanstore_user, oceanstore_password, session)  # 存储账号
        if not login_deviceId:
            print(f'[ERROR] 获取deviceId失败, 跳过!!!')
            # exit(22)  # 脚本里不能exit非0，否则工单会异常
            return

        lun_info_url = f'{oceanstore_url}/deviceManager/rest/{login_deviceId}/lun' # 查询所有lun 批量查询FILESYSTEM和LUN时，如果不指定查询范围，默认查询1500条
        lun_info = session.get(lun_info_url, headers = {"iBaseToken": login_iBaseToken}, cookies=login_cookies, timeout=10)
        lun_data_all = json.loads(lun_info.text)['data']
        print(f'当前{platform_name}[{oceanstore_url}]存储平台lun总数: {len(lun_data_all)}')
        # 查找所有非自动迁移的lun
        for lun in lun_data_all:
            lun_id = lun['ID']
            lun_name = lun['NAME']
            lun_description = lun['DESCRIPTION']
            lun_relocation_policy = lun['DATATRANSFERPOLICY']
            lun_smartcachestate = lun['SMARTCACHESTATE']
            if "k8s" in lun_name:
                continue
            if lun_description != 'Created from Kubernetes CSI':
                continue
            if lun_name in blacklist:
                continue

            if lun_relocation_policy != '1':
                tofix_automatic_luns.append(lun)
            # 注意只针对oceanstore5610-bj处理smartcache
            if platform_name == "oceanstore5610-bj" and lun_smartcachestate != '1':
                tofix_smartcache_lunns.append(lun)
            else: 
                continue

            # if lun_description == "Created from Kubernetes CSI":
            #     tofix_automatic_luns.append(lun)
            # else:
            #     other_not_automatic_luns.append(lun)
            print(f'id:{lun_id} lun:{lun_name} relocation_policy:{lun_relocation_policy} smartcachestate:{lun_smartcachestate}')

        # if len(other_not_automatic_luns) != 0:
        #     lun_id_list = ','.join([lun['ID'] for lun in other_not_automatic_luns])
        #     print(f'[待修改] 存在{len(other_not_automatic_luns)}个其他lun待修改, 手动批量修改lun命令: change lun lun_id_list={lun_id_list} relocation_policy=automatic')
        #     print(f'查看lun命令: show lun general lun_id=LUN_ID')

        # 调用接口修改lun的relocation_policy为automatic
        if len(tofix_automatic_luns) != 0:
            for lun in tofix_automatic_luns:
                lun_id = lun['ID']
                lun_name = lun['NAME']
                body = {
                    "ID": lun_id,
                    "DATATRANSFERPOLICY": "1"   # 只修改数据传输策略为自动迁移
                }
                lun_fix_url = f'{oceanstore_url}/deviceManager/rest/{login_deviceId}/lun/{lun_id}'
                res = session.put(lun_fix_url, data=json.dumps(body), headers = {"iBaseToken": login_iBaseToken}, cookies=login_cookies)
                res_json = json.loads(res.text)
                if res.status_code != 200:
                    print(f'[ERROR] put id:{lun_id} lun:{lun_name} relocation_policy failed: {res.status_code}')
                    print(f'脚本执行中断, 请手动处理: change lun lun_id={lun_id} relocation_policy=automatic')
                    break
                elif res_json.get('error').get('code') != 0:
                    description = res_json.get('error').get('description')
                    print(f'[ERROR] put id:{lun_id} lun:{lun_name} relocation_policy error:  {description}')
                    print(f'脚本执行中断, 请手动处理: change lun lun_id={lun_id} relocation_policy=automatic')
                    break
            else:
                print(f'[已修改] {len(tofix_automatic_luns)}个csi创建的lun已修改relocation_policy为automatic!')
        
        # 调用接口修改lun的smartcache
        if len(tofix_smartcache_lunns) != 0:
            for lun in tofix_smartcache_lunns:
                lun_id = lun['ID']
                lun_name = lun['NAME']
                body = {
                    "ID": "0",     # 0: smartcache partition id 只有0
                    "TYPE": 273,   # 273：SmartCache分区；接口文档里没有可能不需要
                    "ASSOCIATEOBJTYPE": 11,     # 11: 关联对象类型lun
                    "ASSOCIATEOBJID": lun_id,   # 关联对象类型id
                }
                print(f'id:{lun_id} lun:{lun_name} smartcachestate:{lun["SMARTCACHESTATE"]}')
                lun_fix_url = f'{oceanstore_url}/deviceManager/rest/{login_deviceId}/SMARTCACHEPARTITION/CREATE_ASSOCIATE'
                res = session.put(lun_fix_url, data=json.dumps(body), headers = {"iBaseToken": login_iBaseToken}, cookies=login_cookies)
                res_json = json.loads(res.text)
                if res.status_code != 200:
                    print(f'[ERROR] put id:{lun_id} lun:{lun_name} smartcache failed: {res.status_code}')
                    print(f'脚本执行中断, 请手动处理: add smart_cache_partition lun smart_cache_partition_id=0 lun_id_list={lun_id}')
                    break
                elif res_json.get('error').get('code') != 0:
                    description = res_json.get('error').get('description')
                    print(f'[ERROR] put id:{lun_id} lun:{lun_name} smartcache error:  {description}')
                    print(f'脚本执行中断, 请手动处理: add smart_cache_partition lun smart_cache_partition_id=0 lun_id_list={lun_id}')
                    break
            else:
                print(f'[已修改] {len(tofix_smartcache_lunns)}个csi创建的lun已修改smart_cache_partition_id=0!')

    except Exception as e:
        print("fix_huawei_lun执行异常: ", str(e))
    finally:
        session.close()



print('start fix huawei csi lun...')

for name, item in oceanstore_platforms.items():
    fix_huawei_lun(name, item['url'],item['user'], item['password'])
