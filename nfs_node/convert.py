# -*- coding: utf-8 -*-
import os
import subprocess
import time
import json
import sys
import shutil
import fcntl

def execute_command(command, log_file_path):
    with open(log_file_path, 'a') as lf:
        lf.write("Executing command: {}\n".format(command))
        try:
            subprocess.check_call(command, shell=True, stdout=lf, stderr=lf)
            return True
        except subprocess.CalledProcessError as e:
            lf.write("Error executing command: {}\n{}\n".format(command, e))
            return False

def update_log(log_file, log):
    with open(log_file, 'w') as f:
        json.dump(log, f, ensure_ascii=False, indent=4)

def read_log(log_file):
    if os.path.exists(log_file):
        with open(log_file, 'r') as f:
            return json.load(f)
    return None

def backup_log_file(log_file):
    if os.path.exists(log_file):
        backup_file = "{}.backup_{}.log".format(log_file, int(time.time()))
        shutil.move(log_file, backup_file)

def acquire_lock(lock_file_path):
    lock_file = open(lock_file_path, 'w')
    try:
        fcntl.lockf(lock_file, fcntl.LOCK_EX | fcntl.LOCK_NB)
        return lock_file
    except IOError:
        print("Another instance of the script is already running. Exiting.")
        sys.exit(0)

def release_lock(lock_file):
    lock_file.close()

def process_vhd_files(directory):
    lock_file_path = os.path.join(directory, ".lock")
    lock_file = acquire_lock(lock_file_path)

    overall_log = {
        "status": "processing",
        "start_time": "",
        "end_time": "",
        "total_elapsed_time": 0,
	"total_elapsed_time_formatted": "",
        "files_processed": [],
        "error": ""
    }

    overall_log_file = os.path.join(directory, "result.json")
    update_log(overall_log_file, overall_log)

    try:
        start_time = int(time.time())
        overall_log["start_time"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))

        process_log_file = os.path.join(directory, "process.log")
        backup_log_file(process_log_file)

        with open(process_log_file, 'w') as f:
            f.write("Processing started at: {}\n".format(overall_log["start_time"]))
            f.write("\n==================================================\n\n")

        use_qemu_img = False
        vhd_files = sorted([f for f in os.listdir(directory) if f.endswith(".vhd")])

        if len(vhd_files) > 2:
            use_qemu_img = True

        for vhd_file in vhd_files:
            vhd_file_path = os.path.join(directory, vhd_file)
            log_file = "{}.json".format(vhd_file_path)
            log = read_log(log_file) or {
                "status": "",
                "convert_start": 0,
                "convert_end": 0,
                "compress_start": 0,
                "compress_end": 0,
            }

            # Generate qcow2 and min.qcow2 file names
            base_name = vhd_file.rsplit('.', 1)[0]
            qcow2_file =  os.path.join(directory,"{}.qcow2".format(base_name))
            min_qcow2_file =  os.path.join(directory,"{}.min.qcow2".format(base_name))

            # Convert VHD to QCOW2
            if log["status"] in ["convert_ok", "compress_ok", "compress_ing", "compress_err"]:
                with open(process_log_file, 'a') as f:
                    f.write("Skipping conversion of {} because it is already in status: {}\n".format(vhd_file, log['status']))
                    f.write("\n==================================================\n\n")
            else:
                log["convert_start"] = int(time.time())
                log["convert_end"] = 0
                log["compress_start"] = 0
                log["compress_end"] = 0
                log["status"] = "convert_ing"
                update_log(log_file, log)
                with open(process_log_file, 'a') as f:
                    f.write("Starting conversion of {} at {}\n".format(vhd_file, time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(log['convert_start']))))
                if execute_command("qemu-img-hw convert -O qcow2 {} {} -p".format(vhd_file_path, qcow2_file), process_log_file):
                    log["convert_end"] = int(time.time())
                    log["convert_elapsed_time"] = log["convert_end"] - log["convert_start"]
                    log["status"] = "convert_ok"
                    with open(process_log_file, 'a') as f:
                        f.write("Completed conversion of {} at {}\n".format(vhd_file, time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(log['convert_end']))))
                        f.write("\n==================================================\n\n")
                else:
                    log["convert_end"] = int(time.time())
                    log["convert_elapsed_time"] = log["convert_end"] - log["convert_start"]
                    log["status"] = "convert_err"
                    overall_log["status"] = "convert_err"
                    overall_log["error"] = "Conversion error for file: {}".format(vhd_file)
                    update_log(log_file, log)
                    update_log(overall_log_file, overall_log)
                    release_lock(lock_file)
                    return

                update_log(log_file, log)


            # Check file size and rename if less than noCompressSize GB
            noCompressSize = 100 # GB
            qcow2_size = os.path.getsize(qcow2_file)
            if qcow2_size <= noCompressSize * 1024 * 1024 * 1024:
                os.link(qcow2_file, min_qcow2_file)
                log["compress_start"] = int(time.time())
                log["compress_end"] = int(time.time())
                log["compress_elapsed_time"] = log["compress_end"] - log["compress_start"]
                log["status"] = "compress_ok"
                with open(process_log_file, 'a') as f:
                    f.write("File size is less than {}GB, copy {} to {}\n".format(noCompressSize, qcow2_file, min_qcow2_file))
                    f.write("\n==================================================\n\n")
            else:
                # Compress QCOW2
                if log["status"] in ["compress_ok"]:
                    with open(process_log_file, 'a') as f:
                        f.write("Skipping compression of {} because it is already in status: {}\n".format(qcow2_file, log['status']))
                        f.write("\n==================================================\n\n")
                else:
                    log["compress_start"] = int(time.time())
                    log["compress_end"] = 0
                    log["status"] = "compress_ing"
                    update_log(log_file, log)
                    with open(process_log_file, 'a') as f:
                        f.write("Starting compression of {} at {}\n".format(qcow2_file, time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(log['compress_start']))))
                    if use_qemu_img:
                        if execute_command("qemu-img convert -c -p -f qcow2 -O qcow2 {} {}".format(qcow2_file, min_qcow2_file), process_log_file):
                            log["compress_end"] = int(time.time())
                            log["compress_elapsed_time"] = log["compress_end"] - log["compress_start"]
                            log["status"] = "compress_ok"
                            with open(process_log_file, 'a') as f:
                                f.write("Completed compression of {} at {}\n".format(qcow2_file, time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(log['compress_end']))))
                                f.write("\n==================================================\n\n")
                        else:
                            log["compress_end"] = int(time.time())
                            log["compress_elapsed_time"] = log["compress_end"] - log["compress_start"]
                            log["status"] = "compress_err"
                            overall_log["status"] = "compress_err"
                            overall_log["error"] = "Compression error for file: {}".format(qcow2_file)
                            update_log(log_file, log)
                            update_log(overall_log_file, overall_log)
                            release_lock(lock_file)
                            return
                    else:
                        if execute_command("virt-sparsify --compress {} {}".format(qcow2_file, min_qcow2_file), process_log_file):
                            log["compress_end"] = int(time.time())
                            log["compress_elapsed_time"] = log["compress_end"] - log["compress_start"]
                            log["status"] = "compress_ok"
                            with open(process_log_file, 'a') as f:
                                f.write("Completed compression of {} at {}\n".format(qcow2_file, time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(log['compress_end']))))
                                f.write("\n==================================================\n\n")
                        else:
                            log["compress_end"] = int(time.time())
                            log["compress_elapsed_time"] = log["compress_end"] - log["compress_start"]
                            log["status"] = "compress_err"
                            overall_log["status"] = "compress_err"
                            overall_log["error"] = "Compression error for file: {}".format(qcow2_file)
                            update_log(log_file, log)
                            update_log(overall_log_file, overall_log)
                            release_lock(lock_file)
                            return

                    update_log(log_file, log)

            overall_log["files_processed"].append({
                "file": str(vhd_file),
                "status": log["status"],
                "convert_start": log["convert_start"],
                "convert_end": log["convert_end"],
                "convert_elapsed_time": log["convert_elapsed_time"],
                "convert_elapsed_time_formatted": "{x_minutes}分{x_seconds}秒".format(x_minutes=log["convert_elapsed_time"] // 60,x_seconds= log["convert_elapsed_time"] % 60),
                "compress_start": log["compress_start"],
                "compress_end": log["compress_end"],
                "compress_elapsed_time": log["compress_elapsed_time"],
                "compress_elapsed_time_formatted": "{x_minutes}分{x_seconds}秒".format(x_minutes=log["compress_elapsed_time"] // 60,x_seconds= log["compress_elapsed_time"] % 60)
            })
            update_log(overall_log_file, overall_log)


        if overall_log["status"] == "processing":
            overall_log["status"] = "ok"


        overall_log["end_time"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(time.time())))
        overall_log["total_elapsed_time"] = int(time.time()) - start_time
        overall_log["total_elapsed_time_formatted"] = "{x_minutes}分{x_seconds}秒".format(x_minutes=overall_log["total_elapsed_time"] // 60,x_seconds= overall_log["total_elapsed_time"] % 60)

        print("全部处理完成: {}".format(overall_log["end_time"]))
        print("总体耗时: {} 秒".format(overall_log["total_elapsed_time"]))

        with open(process_log_file, 'a') as f:
            f.write("Processing ended at: {}\n".format(overall_log["end_time"]))
            f.write("Total elapsed time: {} seconds\n".format(overall_log["total_elapsed_time"]))
            f.write("\n==================================================\n\n")

        # Write overall log file
        update_log(overall_log_file, overall_log)
    except KeyboardInterrupt:
        overall_log["status"] = "error"
        overall_log["error"] = "Processing interrupted by user."
        print(overall_log["error"])
        overall_log["end_time"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(time.time())))
        overall_log["total_elapsed_time"] = int(time.time()) - start_time
        update_log(overall_log_file, overall_log)
    except Exception as e:
        overall_log["status"] = "error"
        overall_log["error"] = str(e)
        print("Error occurred: ", e)
        overall_log["end_time"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(time.time())))
        overall_log["total_elapsed_time"] = int(time.time()) - start_time
        update_log(overall_log_file, overall_log)
    finally:
        release_lock(lock_file)
        if overall_log["status"] != "ok":
            overall_log["end_time"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(time.time())))
            overall_log["total_elapsed_time"] = int(time.time()) - start_time
            overall_log["total_elapsed_time_formatted"] = "{x_minutes}分{x_seconds}秒".format(x_minutes=overall_log["total_elapsed_time"] // 60,x_seconds= overall_log["total_elapsed_time"] % 60)

	    update_log(overall_log_file, overall_log)
            sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("错误: 请指定一个目录")
        sys.exit(1)

    input_path = os.path.abspath(sys.argv[1])

    if os.path.isdir(input_path):
        process_vhd_files(input_path)
    else:
        print("错误: 指定的路径不存在")
        sys.exit(1)