#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

import requests
import json
import ssl
# import paramiko
from requests.packages.urllib3.exceptions import InsecureRequestWarning


'''
@ 获取东海存储5610所有lun的使用情况及对应的pvc

@ 执行:
  环境: 10.10.22.225
  /home/<USER>/hff/python
  python3.6 get_oceanstore_lun_usage.py
'''


oceanstore_platforms = {
    "oceanstore5610": {
            'url': 'https://192.168.55.5:8088',
            'user': 'sysuser02',
            'password': '******'
        }
    ,
    "oceanstore5310": {
        'url': 'https://192.168.55.3:8088',
        'user': 'sysuser02',
        'password': '******'
    }
    
}


# 登录https站点时不弹出警告信息
ssl._create_default_https_context = ssl._create_unverified_context
requests.packages.urllib3.disable_warnings(
    InsecureRequestWarning)  # 屏蔽访问过程中的warning信息

def login(url, username, passwd, session):
    url_session = url + '/deviceManager/rest/xxxxx/sessions'
    body = {
        'username': username,
        'password': passwd,
        'scope': '0'
    }
    resp = session.post(url_session, data=json.dumps(body), verify=False)
    if resp.json().get('error')['code'] != 0:
        print('登录失败: {}'.format(resp.json().get('error')['code']))
        resp = session.post(url_session, data=json.dumps(body), verify=False)
    res_data = json.loads(resp.text)['data']
    deviceId = res_data.get('deviceid', '')
    iBaseToken = res_data.get('iBaseToken', '')
    cookies = resp.cookies
    if not deviceId:
        print(f'[ERROR] 获取deviceId失败')
    return deviceId, session, iBaseToken, cookies


def main():

    for platform_name, platform_info in oceanstore_platforms.items():
        oceanstore_url = platform_info['url']
        oceanstore_user = platform_info['user']
        oceanstore_password = platform_info['password']

        # ssh = paramiko.SSHClient()
        # ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        # ssh.connect(hostname='**********', username='dapp', port=22, password=ckv_node_password, timeout=10)

        session = requests.Session()
        deviceId, s, b, c = login(oceanstore_url, oceanstore_user, oceanstore_password, session)  # 存储账号
        
        lun_info_url = f'{oceanstore_url}/deviceManager/rest/{deviceId}/lun' # 查询所有lun 批量查询FILESYSTEM和LUN时，如果不指定查询范围，默认查询1500条
        lun_info = session.get(lun_info_url, headers = {"iBaseToken": b}, cookies=c)
        lun_data_all = json.loads(lun_info.text)['data']
        print(f'当前{platform_name}[{oceanstore_url}]存储平台lun总数: {len(lun_data_all)}')
        # 所有lun按exposedtoinitiaor的大小排序
        lun_data_all.sort(key=lambda x: int(x['ALLOCCAPACITY']), reverse=True)
        print('lun', 'lun id',  '是否映射', 'pvc名', '容量(GB)', '实际占用容量(GB)')
        for lun in lun_data_all:
            lun_id = lun['ID']
            lun_name = lun['NAME']
            sectorsize = int(lun['SECTORSIZE'])
            capacity = int(int(lun['CAPACITY'])*sectorsize/1024/1024/1024)
            alloccapacity = int(int(lun['ALLOCCAPACITY'])*sectorsize/1024/1024/1024)
            if alloccapacity < 200:
                continue
            # exposedtoinitiaor = "是" if lun['EXPOSEDTOINITIATOR'] == "1" else "否"  # 是否映射
            exposedtoinitiaor = lun['EXPOSEDTOINITIATOR']
            # lun_description = lun['DESCRIPTION']
            pvc_name = "TODO"
            # if lun_description == "Created from Kubernetes CSI":
            #     get_pvc_cmd = "kubectl get pv | grep {} | awk '{print $6}'".format(lun_name)
            #     _, stdout, stderr = ssh.exec_command(get_pvc_cmd)
            #     returncode = stdout.channel.recv_exit_status()
            #     if returncode != 0:
            #         print(f'pvc {lun_name}查询失败: ',stderr.read(), stdout.read().decode())
            #         exit(1)
            #     pvc_info = stdout.read().decode()
            #     pvc_name = pvc_info.replace("default/", "")
            print(f'{lun_name}, {lun_id}, {exposedtoinitiaor}, {pvc_name}, {capacity}, {alloccapacity}')

if __name__ == "__main__":
    main()
