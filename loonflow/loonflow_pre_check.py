#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

'''
fusion虚机迁移-前置检查脚本

'''

from workflow.service.ticket.ticket_base_service import TicketBaseService
import paramiko
import json
import requests
import re


# ERP账号密码
yiyunwei_url = "http://aioms.caih.local"
username = "******"
password = "******"
# ckv节点dapp密码
ckv_node = ""   # 从工单获取
ckv_node_password = "******"
# 中转nfs虚机root密码
trans_vm = ""      # 从工单获取
trans_vm_password = "******"
# 华为云用户密码
fusion_addr = ""   # 从工单获取  https://10.10.3.62:8443/ 或https://10.10.31.52:8443/
fusion_authuser = "vmexport"
fusion_authkey = "******"



fusion_platform = {
    "https://10.10.3.62:8443/": {    # 南宁      
        "flag": "4AF909D7",
        "cloud_platform_id": 3,
        "platform_name": "华为云-南宁电信2",
        "pool": "telecom2"
    },
    "https://10.10.31.52:8443/": {  # 北京
        "flag": "380C0718",
        "cloud_platform_id": 7,
        "platform_name": "华为云-北京",
        "pool": "vianet"
    }
}

target_platfrom = {
    "10.10.3.71": {
        "cloud_platform_id": 93,
        "platform_name": "容器云虚拟化(生产)-南宁电信", 
        "pool": "caihcloudpro",
    },
    "***********": {
        "cloud_platform_id": 95,
        "platform_name": "北京机房容器虚拟化集群-测试",
        "pool": "caihcloudbj",  # 测试集群
    },
    "***********-prod": {   # 北京生产比较特殊，命名空间是prod
        "cloud_platform_id": 95,  # TODO，等维侃创建新的平台
        "platform_name": "北京机房容器虚拟化集群-生产",   
        "pool": "caihcloudbjpro",
    },
}


fusion_flag = "" 
fusion_pool = ""
fusion_platform_id = 0

target_cloud_platform_id = 999999
target_pool = ""

def check_mac_address(mac):
    pattern = re.compile("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")
    if pattern.match(mac):
        return True
    else:
        return False

def pre_check():
    pre_check_status = "成功"
    os_type, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'os_type')
    vm_list, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'delvm_list')
    vm_group, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'vm_group')
    mac_addr, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'mac_addr')
    fusion_addr, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'fusion_addr')
    ckv_node, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'ckv_node')
    # 'Cluster05', 'Cluster06', 'Cluster07', 'CaihCluster04-vianet', 'CaihCluster04-vianet'
    migration_cluster, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'migration_cluster')
    is_keep_mac, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'is_keep_mac')

    try:
        if ckv_node == None or ckv_node not in target_platfrom.keys():
            print(f"[失败]请检查目标平台")
            pre_check_status = "失败"
            return

        if fusion_addr not in fusion_platform.keys():
            print(f"[失败]请检查华为云地址{fusion_addr}是否正确")
            pre_check_status = "失败"
            return False
        fusion_flag = fusion_platform[fusion_addr].get("flag")
        fusion_platform_id = fusion_platform[fusion_addr].get("cloud_platform_id")
        # fusion_platform_name = fusion_platform[fusion_addr].get("platform_name")
        fusion_pool = fusion_platform[fusion_addr].get("pool")

        target_cloud_platform_id = target_platfrom[ckv_node].get("cloud_platform_id")
        # target_platform_name = target_platfrom[ckv_node].get("platform_name")
        target_pool = target_platfrom[ckv_node].get("pool")


        # 如果ckv_node带-，则ckv_node取-前面的字符串，命名空间取-后面的字符串
        # 如果ckv_node不带-，则ckv_node取全部字符串，命名空间取default
        namespace = "default"
        if '-' in ckv_node:
            namespace = ckv_node.split('-')[1]
            ckv_node = ckv_node.split('-')[0]
        print(f'虚机命名空间{namespace}: PASS')

        vm_info = json.loads(vm_list)
        ticket_disk_size = 0
        vm_name = ""
        vm_ip = ""
        vm_id = ""  # 工单上的虚机VMID，不要取实时的
        yiyunwei_os = ""
        host_id=999999999 
        
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        for n in vm_info:
            vm_ip = n.get('ip')
            vm_name = n.get('hostname').replace('_', '-').lower()
            vm_id = n.get('vm_id')
            host_id = n.get('id')
            if n.get('data_disk') != None and n.get('data_disk') != '':
                ticket_disk_size += int(n.get('data_disk'))
            if n.get('system_disk') != None:
                ticket_disk_size += int(n.get('system_disk'))
            ticket_disk_size = ticket_disk_size/1024
            for item in n.get('attribute_value'):
                if item.get('attr').get('attr_name') == 'VM_ID':
                    vm_id = item.get('stringvalue')
        if vm_id == None or vm_id == '':
            print("[失败]VM ID检查失败")
            pre_check_status = "失败"
        else:
            print("VM ID检查: PASS")

        # 检察虚机是否已创建
        ssh.connect(hostname=ckv_node, username='dapp', port=22, password=ckv_node_password, timeout=10)
        cmdStr = f"sudo /usr/bin/kubectl get vm -n {namespace} {vm_name}"
        _, stdout, stderr = ssh.exec_command(cmdStr)
        returncode = stdout.channel.recv_exit_status()
        if returncode != 0:
            print(f"虚拟机{namespace}/{vm_name}未创建: PASS")
        else:
            print(f"[失败]虚机{namespace}/{vm_name}已创建！")
            pre_check_status = "失败"

        # 检查虚拟机分组是否存在
        if vm_group != None and vm_group != '':
            # 判断vm_group是否包含大写和下划线和空格
            if re.search(r'[A-Z_ ]', vm_group):
                print(f"[失败]虚拟机分组{vm_group}不能包含大写字母、下划线、空格")
                pre_check_status = "失败"
            else:
                #ssh.connect(hostname=ckv_node, username='dapp', port=22, password=ckv_node_password, timeout=10)
                cmdStr = 'sudo /usr/bin/kubectl get vmgroups -n %s %s  || echo \'{"apiVersion": "caihckv.io/v1", "kind": "VmGroup", "metadata": {"name": "%s"}, "status": {"phase": "Success"}, "vms":[]}\' | sudo /usr/bin/kubectl apply -n %s -f -' % (namespace, vm_group, vm_group, namespace)
                _, stdout, stderr = ssh.exec_command(cmdStr)
                returncode = stdout.channel.recv_exit_status()
                if returncode != 0:
                    print(f"[失败]虚拟机分组{namespace}/{vm_group}创建失败, 请先手动创建！({stderr.read()})")
                    pre_check_status = "失败"
                else:
                    print(f"虚拟机分组{namespace}/{vm_group}检查: PASS")

        fusion_session = requests.session()
        fusion_login_url =f'{fusion_addr}service/login/form'
        data = {"acceptLanguage": "zh-CN",
        "authKey": fusion_authkey,
        "authType": "0",
        "authUser": fusion_authuser,
        "userType": "0",
        "verification": ""}
        fusion_session.headers["Content-Type"]="application/json"
        res = fusion_session.post(fusion_login_url, data=json.dumps(data), verify=False, timeout=10)
        if res.status_code != 200:
            print("华为云认证接口调用失败")
            return False
        elif res.json().get('errorDes') is not None:
            print("华为云认证接口报错: ", res.json().get('errorDes'))
            return False
        fusion_session.headers['CSRF-HW'] = res.json()['csrfToken']

        # 检查虚机所在集群是否cluster06,如果不是则退出
        # 如果虚机磁盘大小与工单虚机不一致，则退出
        fusion_url = f"{fusion_addr}service/sites/{fusion_flag}/vms/{vm_id}/"
        res = fusion_session.get(fusion_url, verify=False, timeout=10)
        if res.status_code != 200:
            print("[失败]查询虚机集群信息接口调用失败")
            pre_check_status = "失败"
        elif res.json().get('errorDes') != None:
            print("[失败]查询虚机集群信息接口报错: ", res.json().get('errorDes'))
            pre_check_status = "失败"
        else:
            actual_disk_size = 0
            vmConfig=res.json().get('vmConfig')
            disks=vmConfig.get('disks')
            for disk in disks:
                actual_disk_size += disk.get('quantityGB')
            if actual_disk_size != ticket_disk_size:
                print(f"[失败]虚机磁盘大小[{actual_disk_size}]与工单磁盘大小[{ticket_disk_size}]不一致,可能存在未卸载的盘")
                pre_check_status = "失败"
            else:
                print(f"虚机磁盘大小检查[{actual_disk_size}]: PASS")
            clusterName = res.json().get('clusterName')
            if clusterName != migration_cluster:
                print(f"[失败]虚机所在集群[{clusterName}]非{migration_cluster}, 请先热迁到{migration_cluster}")
                pre_check_status = "失败"
            else:
                print("fusion虚机在{}上: PASS".format(clusterName))

            # 检查mac地址是否合法
            if is_keep_mac == "is_keep_mac":
                nics = vmConfig.get('nics')
                old_mac_addr = "+".join([nic.get('mac') for nic in nics if nic.get('mac') != ""])
                TicketBaseService.update_ticket_field_value(ticket_id, {"mac_addr":old_mac_addr})
                print(f"保留原mac地址{old_mac_addr}: PASS")
            elif mac_addr != None and mac_addr != '' and check_mac_address(mac_addr) == False:
                print("[失败]mac地址{}不合法".format(mac_addr))
                pre_check_status = "失败"
            else:
                print("mac地址检查: PASS")

        # 易运维认证
        # 易运维生产地址
       
        yiyunwei_session = requests.session()
        yiyunwei_session.headers['Content-Type'] = 'application/json'
        res = yiyunwei_session.post(f'{yiyunwei_url}/token/', data=json.dumps({"username": username, "password": password}))
        
        if res.status_code != 200:
            print("易运维认证失败：{}".format(res.status_code))
            pre_check_status = "失败"
        else:
            token = res.json()['access']
            yiyunwei_session.headers['Authorization'] = f'Bearer {token}'

            # 查询易运维虚机信息以获取操作系统 centos7/centols7/ubuntu/windows 
            res = yiyunwei_session.get(f'{yiyunwei_url}/api/hosts/?status=1&ip={vm_ip}')
            for host in res.json()['results']:
                if host["id"] == host_id:
                    if host.get("os") == None:
                        yiyunwei_os = ""
                    else:
                        yiyunwei_os = host.get("os").replace(" ", "").lower()
                    break
            yiyunwei_patch_vm_url = f'{yiyunwei_url}/host-info-tabs/{host_id}?asset_type=Virtual%20machine&edit=true'
            if yiyunwei_os == "" or yiyunwei_os == None:
                print(f'[失败]易运维查询操作系统失败')
                print(f'易运维主机详情: {yiyunwei_patch_vm_url}')
                pre_check_status = "失败"
            elif os_type in yiyunwei_os:
                print(f'虚机操作系统{yiyunwei_os}: PASS')
            elif res.status_code != 200:
                print("[失败]易运维查询失败")
                pre_check_status = "失败"
            else:
                print(f'[失败]虚机操作系统{yiyunwei_os}与工单操作系统{os_type}不一致')
                print(f'易运维主机详情: {yiyunwei_patch_vm_url}')
                pre_check_status = "失败"

            if pre_check_status != "失败":
                 # 修改易运维信息
                new_vm_id = f'{namespace}/{vm_name.replace("_", "-").lower()}'
                body = {
                    "cloud_platform": target_cloud_platform_id, # 容器云虚拟化(生产)-南宁电信
                    "attribute_value": [
                        {"attr":8,"stringvalue": new_vm_id}, # VM_ID
                        {"attr":12,"stringvalue": target_pool}, # POOL
                        {"attr":19,"stringvalue":"true"} # 是否计费
                    ]
                }
                res = yiyunwei_session.patch(url=f'{yiyunwei_url}/api/hosts/{host_id}/', data=json.dumps(body))
                if res.status_code != 200:
                    print(f'[失败]修改易运维主机失败: [{str(res.status_code)}] {res.text}')
                    pre_check_status = "失败"
                else:
                    print(f'[易运维主机信息已修改]: [VM ID]  {vm_id}->{new_vm_id}')
                    print(f'[易运维主机信息已修改]: [云平台id]  {fusion_platform_id}->{target_cloud_platform_id}')
                    print(f'[易运维主机信息已修改]: [POOL] {fusion_pool}->{target_pool}')
                    print(f'易运维主机详情: {yiyunwei_patch_vm_url}')
    except Exception as e:
        print("[失败]前置检查脚本执行异常: ", str(e))
        pre_check_status = "失败"
    finally:
        ssh.close()
        fusion_session.close()
        yiyunwei_session.close()
        TicketBaseService.update_ticket_field_value(ticket_id, {"pre_check_status":pre_check_status})

pre_check()
