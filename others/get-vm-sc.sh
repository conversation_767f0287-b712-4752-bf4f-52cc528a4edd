#!/bin/bash

# 查询k8s 所有vm(kubectl get vmi -A )资源里虚机挂载卷.spec.volumes.persistentVolumeClaim.claimName对应的存储类名称，得出结果列表例如：命名空间/虚拟机名1-虚拟机IP：存储类1，存储类2 ...

# 获取所有 VMI 并逐个处理
kubectl get vmi -A -o json | jq -c '.items[]' | while read -r vmi; do
  ns=$(echo "$vmi" | jq -r '.metadata.namespace')
  name=$(echo "$vmi" | jq -r '.metadata.name')
  # 获取第一个接口的 IP，如无 IP 则返回 "N/A"
  ip=$(echo "$vmi" | jq -r '.status.interfaces[0].ipAddress // "N/A"')
  
  # 筛选出所有挂载卷中定义了 persistentVolumeClaim 的项，获取 claimName
  claims=$(echo "$vmi" | jq -r '.spec.volumes[]? | select(.persistentVolumeClaim != null) | .persistentVolumeClaim.claimName')
  
  # 如果当前 VMI 没有挂载 PVC，则跳过
  if [ -z "$claims" ]; then
    continue
  fi

  storageClasses=""
  for claim in $claims; do
    # 查询对应 PVC 的 storageClassName（假定 PVC 与 VMI 在同一命名空间）
    sc=$(kubectl get pvc "$claim" -n "$ns" -o jsonpath='{.spec.storageClassName}' 2>/dev/null)
    if [ -z "$sc" ]; then
      sc="(未找到)"
    fi
    if [ -z "$storageClasses" ]; then
      storageClasses="$sc"
    else
      storageClasses="$storageClasses, $sc"
    fi
  done
  
  # 输出格式：命名空间/虚机名(虚机IP): 存储类1，存储类2 ...
  echo "${ns}/${name}(${ip}): ${storageClasses}" 
done
