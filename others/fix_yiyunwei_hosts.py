#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

import requests
import json
import ssl
from requests.packages.urllib3.exceptions import InsecureRequestWarning


requests.packages.urllib3.disable_warnings(InsecureRequestWarning)  # 屏蔽访问过程中的warning信息

'''
@ 修正易运维主机的信息
遍历所有容器云虚机（生产/测试）,如果虚机的平台/POOL/vmid非预期,则修改易运维信息，并打印虚机编辑列表

1. 在当前目录下创建./ckv_vm_pro 和 ./ckv_vm_test两个文本
每行添加一行虚机信息： {虚机名}:{虚机IP} 以冒号分割
kubectl get vmi | awk '{print $1":"$4}' |  grep -v NAME   (可能部分IP需要修正一下)

执行脚本: python fix_yiyunwei_hosts.py
'''
username = "******"
password = "******"
yyw_url = 'http://**********'


file = open('./ckv_vm_pro', 'r')  
ckv_vm_pro = file.readlines()    
file.close()                   

file = open('./ckv_vm_test', 'r') 
ckv_vm_test = file.readlines()   
file.close()                   

all_vm_list = []
all_vm_list = ckv_vm_pro + ckv_vm_test

session = requests.session()
session.headers['Content-Type'] = 'application/json'
res = session.post(f'{yyw_url}/token/', data=json.dumps({"username": username, "password": password}))

try:
    if res.status_code != 200:
        print("认证失败：{}".format(res.status_code))
        exit(1)
    token = res.json()['access']
except Exception as e:
    print(str(e))
    print('认证异常...')
    exit(1)  
session.headers['Authorization'] = f'Bearer {token}'


ckv_vm_err_list = []
for vm_str in all_vm_list:
    if vm_str.strip() == "":
        continue
    old_vm_id = ""
    old_pool = ""
    old_cloud_platform = 9999999
    new_pool = "caihcloud"
    new_cloud_platform = 15  # 容器云虚拟化(测试)-南宁电信
    if vm_str in ckv_vm_pro:
        new_pool = "caihcloudpro"
        new_cloud_platform = 93  # 容器云虚拟化(生产)-南宁电信
    elif vm_str in ckv_vm_test:
        new_pool = "caihcloud"
        new_cloud_platform = 15  # 容器云虚拟化(测试)-南宁电信
    else:
        print(f'[异常]{vm_str}')
        continue

    vm_str = vm_str.strip()  # 去除空格
    # vm_ip 取 vm_str字符串以:分割的第二个字符串
    vm_name = vm_str.split(":")[0]
    vm_ip = vm_str.split(":")[1]
    

    # 查询易运维虚机信息
    get_vm_res = session.get(f'{yyw_url}/api/hosts/?status=1&&ip={vm_ip}')
    results = []

    for host in get_vm_res.json()['results']:
        if host["ip"] == vm_ip: 
            results.append(host)
    if len(results) == 0:
        print(f'[易运维虚机IP找不到/非使用中] {vm_ip}/{vm_name}, 请手动处理！！！！！')
        continue
    elif len(results) > 1:
        print(f'[易运维存在多个相同IP虚机] {vm_ip}/{vm_name}, 请手动处理！！！！！')
        continue

    host= results[0]
    host_id = host["id"]
    hostname = host["hostname"].replace("_", "-").lower()  # 转小写 中横线
    fix_name = ""
    if hostname != vm_name:
        print(f'[易运维虚机名不一致]:{vm_ip}/{vm_name}, {hostname} != {vm_name}, 请手动处理！！！！！')
        continue
    new_vm_id = "default/"+vm_name
    for _attr in host['attribute_value']:
        if _attr['attr']['attr_name'] == 'VM_ID':
            old_vm_id = _attr["stringvalue"]
        if _attr['attr']['attr_name'] == 'POOL':
            old_pool = _attr["stringvalue"]
    if host['cloud_platform'] == None:
        old_cloud_platform = "空"
    else:
        old_cloud_platform = host['cloud_platform']['id']
    yiyunwei_patch_vm_url = f'http://aioms.caih.local/host-info-tabs/{host_id}?asset_type=Virtual%20machine&edit=true'
    if old_vm_id != new_vm_id or old_pool != new_pool or old_cloud_platform != new_cloud_platform:
        # 修改易运维信息 屏蔽-预修改: 
        # body = {
        #     "cloud_platform": new_cloud_platform, 
        #     "attribute_value": [
        #         {"attr":8,"stringvalue": new_vm_id}, # VM_ID
        #         {"attr":12,"stringvalue": new_pool}, # POOL
        #         {"attr":19,"stringvalue":"true"} # 是否计费
        #             ]
        #         }
        # patch_res = session.patch(url=f'{yyw_url}/api/hosts/{host_id}/', data=json.dumps(body))
        # if patch_res.status_code != 200:
        #     print(f'[修改失败]  {vm_name}: [{str(patch_res.status_code)}] {patch_res.text}')
        #     exit(1)
        print(f'[修改虚机] {vm_ip}/{vm_name}: ({old_pool} {old_cloud_platform} {old_vm_id}) >> ({new_pool} {new_cloud_platform} {new_vm_id} )  详情见: {yiyunwei_patch_vm_url}')
        ckv_vm_err_list.append(vm_name)

print(f'已修改{len(ckv_vm_err_list)}个虚机的信息')
