#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

'''
fusion虚机迁移-虚机创建
远程执行虚机创建

'''

from workflow.service.ticket.ticket_base_service import TicketBaseService
import paramiko
import json


# 任务状态：
## 成功
## 进行中
## 失败

# ckv节点dapp密码
ckv_node = ""     # 从工单获取
ckv_node_password = 'XXXXXX'
# 中转nfs虚机root密码
trans_vm = ""      # 从工单获取
trans_vm_password = 'XXXXXXXX'

def create_vm():
    # 获取工单信息ip地址信息
    # ticket_id会通过exec传过来
    # 你也可以获取工单的其他字段信息，包括自定义字段的值。根据获取的值做后续处理

    trans_vm, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'trans_vm')
    vm_list, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'delvm_list')
    vm_info = json.loads(vm_list)
    for n in vm_info:
        vm_ip = n.get('ip')
        hostname = n.get('hostname').replace('_', '-').lower()
    os_type, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'os_type')
    mac_addr, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'mac_addr')
    storageclass, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'storageclass')
    vm_network, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'vm_network')
    ckv_node, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'ckv_node')


    # 如果ckv_node带-，则ckv_node取-前面的字符串，命名空间取-后面的字符串
    # 如果ckv_node不带-，则ckv_node取全部字符串，命名空间取default
    namespace = "default"
    if '-' in ckv_node:
        namespace = ckv_node.split('-')[1]
        ckv_node = ckv_node.split('-')[0]

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        # 生成vm yaml
        cmdStr = "python /ckv/loonflow/genyaml.py /app/nfs/{}/{}.ovf --vmname {} --namespace {} --network {}/{}  --osType {} --classStorage {}".format(vm_ip, vm_ip, hostname, namespace, namespace, vm_network, os_type, storageclass)
        ssh.connect(hostname=trans_vm, username='root', port=22, password=trans_vm_password, timeout=10)
        if mac_addr != None and mac_addr != '': # 固定mac地址
            print("mac: ", mac_addr)
            cmdStr = "python /ckv/loonflow/genyaml.py /app/nfs/{}/{}.ovf --vmname {} --namespace {} --network {}/{}  --osType {} --classStorage {} --macAddr {}".format(vm_ip, vm_ip, hostname, namespace, namespace, vm_network, os_type, storageclass, mac_addr)
        
        _, stdout, stderr = ssh.exec_command(cmdStr)
        returncode = stdout.channel.recv_exit_status()
        if returncode != 0:
            print("gen yaml error: ", stderr.read())
            TicketBaseService.update_ticket_field_value(ticket_id, {"vm_status":"失败"})
            return
        # 创建vm
        ssh.connect(hostname=ckv_node, username='dapp', port=22, password=ckv_node_password, timeout=10)
        cmdStr = "sudo /ckv/loonflow/kubectl-ckv genvm -f http://{}/{}/genvm.yaml".format(trans_vm, vm_ip)
        _, stdout, stderr = ssh.exec_command(cmdStr)
        returncode = stdout.channel.recv_exit_status()
        if returncode != 0:
            print("kubectl ckv genvm error(skip..): ", stderr.read(), stdout.read().decode())
            TicketBaseService.update_ticket_field_value(ticket_id, {"vm_status":"失败"})
            return
        TicketBaseService.update_ticket_field_value(ticket_id, {"vm_status":"进行中: 创建vm ok"})
    except Exception as e:
        print("执行异常: ", str(e))
        # traceback.print_exc()
        TicketBaseService.update_ticket_field_value(ticket_id, {"vm_status":"失败"})
    finally:
        ssh.close()


create_vm()
