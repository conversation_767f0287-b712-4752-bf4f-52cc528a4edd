import requests
from urllib3.exceptions import InsecureRequestWarning
import json
import csv
import os
import getpass

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)  # 屏蔽访问过程中的warning信息

# 易运维用户名密码，如果使用环境变量，请注释掉
username = ""
password = ""

# 易运维生产地址
url = 'http://aioms.caih.local'

# 只有在username和password未定义时才从环境变量获取或提示用户输入
if 'username' not in locals() or not username:
    username = os.environ.get('aioms_username')
    if not username:
        username = input("请输入用户名: ")

if 'password' not in locals() or not password:
    password = os.environ.get('aioms_password')
    if not password:
        password = getpass.getpass("请输入密码: ")

# fusion平台认证
session = requests.session()
session.headers['Content-Type'] = 'application/json'
res = session.post(f'{url}/token/', data=json.dumps({"username": username, "password": password}))
try:
    token = res.json()['access']
except Exception:
    print('易运维认证失败')
    exit(1)
session.headers['Authorization'] = f'Bearer {token}'

def get_vm_info(ip):
    url = f"http://aioms.caih.local/api/hosts/?search={ip}&limit=15&fields=id,ip,status,idc,hostname,attribute_value,product,cpu_num,memory,system_disk,data_disk,asset_type,os&ordering=ip&asset_type=Virtual+machine&vm_delete_status=1&_limit_search_=1"
    response = session.get(url)
    if response.status_code == 200:
        data = response.json()
        if data['results']:
            # get specified ip result
            for result in data['results']:
                if result['ip'] == ip:
                    return result
    return None

def create_ticket(vm_info, os_type, storageclass, trans_vm, vm_group, vm_network, is_start, migration_cluster, is_stop, fusion_addr, ckv_node, is_keep_mac):
    url = "http://aioms.caih.local/shutongflow/create_ticket/"
    
    payload = {
        "notes": "一个工单提交一个虚机迁移申请，请勿添加多个；工单标题以{虚机名}-{ip}命名",
        "fusion_addr": fusion_addr,
        "ckv_node": ckv_node,
        "delvm_list": json.dumps([vm_info]),
        "storageclass": storageclass,
        "os_type": os_type,
        "vm_network": vm_network,
        "is_start": is_start if is_start in ["is_start", "not_start"] else "not_start",
        "trans_vm": trans_vm,
        "beizhu": "<p>注意存储类设置后不可修改，请确认后再提交; 虚机会自动热迁移到所选集群中</p>",
        "title": f"{vm_info['hostname']}-{vm_info['ip']}",
        "vm_group": vm_group,
        "transition_id": 2265,
        "dynamic_participant": {},
        "workflow_id": 211,
        "migration_cluster": migration_cluster,
        "is_stop": is_stop if is_stop in ["is_stop", "not_stop"] else "is_stop",
        "is_keep_mac": is_keep_mac if is_keep_mac in ["is_keep_mac", "not_keep_mac"] else "not_keep_mac"
    }

    response = session.post(url, json=payload)
    return response.status_code == 200

def clean_value(value):
    """清理带注释的值，返回实际需要的值"""
    return value.split(' (')[0].strip() if value else value

def main():
    # 假设文件名为 vm_list.csv
    with open('vm_list.csv', 'r', newline='') as file:
        csv_reader = csv.reader(file)
        headers = next(csv_reader)  # 读取标题行
        
        # 添加新字段的索引
        is_start_index = headers.index('is_start') if 'is_start' in headers else None
        migration_cluster_index = headers.index('migration_cluster') if 'migration_cluster' in headers else None
        is_stop_index = headers.index('is_stop') if 'is_stop' in headers else None
        fusion_addr_index = headers.index('fusion_addr') if 'fusion_addr' in headers else None
        ckv_node_index = headers.index('ckv_node') if 'ckv_node' in headers else None
        is_keep_mac_index = headers.index('is_keep_mac') if 'is_keep_mac' in headers else None
        
        for row in csv_reader:
            # 检查行是否为空
            if not any(row):
                continue  # 跳过空行
            
            # 确保行有足够的列
            if len(row) < 7:
                print(f"Skipping invalid row: {row}")
                continue
            
            # 对基本字段应用clean_value处理
            ip = clean_value(row[0]) if len(row) > 0 else ""
            os_type = clean_value(row[1]) if len(row) > 1 else ""
            storageclass = clean_value(row[2]) if len(row) > 2 else ""
            trans_vm = clean_value(row[3]) if len(row) > 3 else ""
            vm_group = clean_value(row[4]) if len(row) > 4 else ""
            vm_network = clean_value(row[5]) if len(row) > 5 else ""

            # vm_group去掉空格，大写转小写，下划线转横线
            vm_group = vm_group.replace(' ', '').lower().replace('_', '-')

            # 对可选字段应用clean_value处理
            is_start = clean_value(row[is_start_index]) if is_start_index is not None and len(row) > is_start_index else "not_start"
            migration_cluster = clean_value(row[migration_cluster_index]) if migration_cluster_index is not None and len(row) > migration_cluster_index else None
            is_stop = clean_value(row[is_stop_index]) if is_stop_index is not None and len(row) > is_stop_index else "not_stop"

            # 获取新字段的值，设置默认值
            fusion_addr = clean_value(row[fusion_addr_index]) if fusion_addr_index is not None and len(row) > fusion_addr_index else "https://***********:8443/"
            ckv_node = clean_value(row[ckv_node_index]) if ckv_node_index is not None and len(row) > ckv_node_index else "***********"

            # 修改默认值为 is_keep_mac
            is_keep_mac = clean_value(row[is_keep_mac_index]) if is_keep_mac_index is not None and len(row) > is_keep_mac_index else "is_keep_mac"
            
            # 检查 IP 是否为空
            if not ip:
                print("Skipping row with empty IP")
                continue
            
            vm_info = get_vm_info(ip)
            if vm_info:
                yiyunwei_os = vm_info.get('os').replace(" ", "").lower()
                if os_type not in yiyunwei_os:
                    print(f"Skipping row with error vm os_type: {ip}/{os_type}, expect: {yiyunwei_os}" )
                    continue
                success = create_ticket(vm_info, os_type, storageclass, trans_vm, vm_group, vm_network, is_start, migration_cluster, is_stop, fusion_addr, ckv_node, is_keep_mac)
                if success:
                    print(f"Successfully created ticket for {ip}")
                else:
                    print(f"Failed to create ticket for {ip}")
            else:
                print(f"Failed to get information for {ip}")

if __name__ == "__main__":
    main()
