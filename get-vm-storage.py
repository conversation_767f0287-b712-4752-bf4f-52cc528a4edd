# -*- coding: utf-8 -*-

import requests, json
from texttable import Texttable
from urllib3.exceptions import InsecureRequestWarning

'''
# 获取华为云(南宁电信2/北京)虚机存储使用信息
@ 执行步骤：
1. 当前目录创建一个vms.txt文件, 每行填入一个虚机IP
2. 账号密码请替换成你自己的ERP账号密码 (通过易运维查询虚机ID)
'''
yyw_url = 'http://aioms.caih.local'
yyw_username = "kailinzhao"
yyw_password = "Zkl3823806"

fusion_authuser = "vmexport"
fusion_authkey = "Vm_export@2024"

'''
3. 需要通生产, 在办公网执行:  python get-vm-storage.py 
'''


requests.packages.urllib3.disable_warnings(InsecureRequestWarning)  # 屏蔽访问过程中的warning信息

# 易运维生产地址
session = requests.session()
session.headers['Content-Type'] = 'application/json'
res = session.post(f'{yyw_url}/token/', data=json.dumps({"username": yyw_username, "password": yyw_password}))
try:
    token = res.json()['access']
except Exception:
    print('[ERROR] 易运维认证失败')
    exit(1)
session.headers['Authorization'] = f'Bearer {token}'

class VmInfo:
    def __init__(self, ip):
        self.ip = ip
        self.vmid = ""
        self.os = "centos"
        self.product = ""
        self.yyw_system_data_disk = 0
        self.huawei_system_data_disk = 0
        self.name = ""
        self.quantityGB = ""
        self.volProvisionGB = ""
        self.userUsedGB = ""
        self.mac = ""
        self.platform_name = "" # 华为云-北京 / 华为云-南宁电信2
        self.fusion_url = "" # 南宁电信2 https://**********:8443/ 或 北京 https://***********:8443/
        self.fusion_flag = "" # 华为云平台标识 南宁电信2 4AF909D7 北京 380C0718

vms = []
with open('./vms.txt', 'r') as file:
    for line in file:
        vmip=line.strip()
        if not vmip:
            continue
        vm=VmInfo(ip=vmip)
        res = session.get(f'{yyw_url}/api/hosts/?status=1&ip={vmip}')
        for host in res.json()['results']:
            for _attr in host['attribute_value']:
                if _attr['attr']['attr_name'] == 'VM_ID':
                    vm.vmid = _attr["stringvalue"]         # VMID
                    vm.os = host['os']                     # 操作系统
                    vm.product = host['product']['name']   # 所属项目
                    vm.yyw_system_data_disk = int(host['system_disk']/1024)+int(host['data_disk']/1024) # 易运维上的磁盘信息（可能与华为云不一致)
                    vm.platform_name = host['cloud_platform']['platform_name'] # 云平台
                    if vm.platform_name == "华为云-北京":
                        vm.fusion_url = "https://***********:8443/"
                        vm.fusion_flag = "380C0718"
                    elif vm.platform_name == "华为云-南宁电信2":
                        vm.fusion_url = "https://**********:8443/"
                        vm.fusion_flag = "4AF909D7"
                    break
        if vm.vmid == "":
            print("[ERROR] {} 虚机VM_ID为空或找不到虚机!!!!!!!!!!".format(vm.ip))
            continue
        vms.append(vm)


# 创建一个Texttable对象
table = Texttable(max_width= 300)
table.add_row(['虚机名', 'IP', 'MAC', '云平台', '所属项目', '操作系统', '磁盘大小(GB)', '磁盘使用量(GB)', '存储侧实际使用量(GB)'])

for vm in vms:
    if vm.fusion_url == "":
        print(f"[ERROR] 虚机{vm.ip}所在平台[{vm.platform_name}]非华为云-南宁电信2/北京, 跳过查询")
        continue
    # fusion平台认证
    data = {"acceptLanguage": "zh-CN",
    "authKey": fusion_authkey,
    "authType": "0",
    "authUser": fusion_authuser,
    "userType": "0",
    "verification": ""}
    session.headers["Content-Type"]="application/json"
    res = session.post(f"{vm.fusion_url}service/login/form", data=json.dumps(data), verify=False)
    session.headers['CSRF-HW'] = res.json()['csrfToken']
    
    url = f"{vm.fusion_url}service/sites/{vm.fusion_flag}/vms/{vm.vmid}/"
    res = session.get(url, verify=False, timeout=10)
    if res.status_code != 200:
        print("[ERROR] fusion平台虚机VM_ID {}({})查询异常：{}".format(vm.vmid, vm.ip, res.status_code))
        continue

    vm.name = res.json().get('name')
    vmConfig = res.json().get('vmConfig')
    
    if vmConfig == None:
        print("[ERROR] fusion平台虚机VM_ID {} ({}) 查询失败: {}".format(vm.vmid, vm.ip, res.json()))
        continue

    nics = vmConfig.get('nics')
    vm.mac = "+".join([nic.get('mac') for nic in nics if nic.get('mac') != ""])

    disks= vmConfig.get('disks')

    for i, disk in enumerate(disks):
        if i != 0:
            vm.quantityGB += "+"
            vm.volProvisionGB += "+"
            vm.userUsedGB += "+"
        vm.quantityGB += str(disk.get('quantityGB'))
        vm.huawei_system_data_disk += int(disk.get('quantityGB'))
        volumeid = disk.get('volumeUrn').split(":")[-1]
        url = f"{vm.fusion_url}service/sites/{vm.fusion_flag}/volumes/{volumeid}?refreshflag=false"
        res = session.get(url, verify=False, timeout=10)

        if res.json().get('userUsedSize') == None or res.json().get('userUsedSize') == -1:
            vm.userUsedGB=disk.get('datastoreName')+"(无法查询)"           # 分布式存储可能无法查询
        else:
            vm.userUsedGB += str(round(res.json().get('userUsedSize')/1024,1)) # 虚拟机中用户已用空间大小

        if res.json().get('volProvisionSize') == None or res.json().get('volProvisionSize') == -1:
            vm.volProvisionGB=disk.get('datastoreName')+"(无法查询)"        # 分布式存储可能无法查询
        else:
            vm.volProvisionGB += str(round(res.json().get('volProvisionSize')/1024,1)) # 卷当前占用的实际空间大小

    if vm.huawei_system_data_disk != vm.yyw_system_data_disk: # 校验是否与易运维一致
        vm.quantityGB += "(华为云[{}GB]与易运维[{}GB]不一致)".format(vm.huawei_system_data_disk, vm.yyw_system_data_disk)
    table.add_row([vm.name, vm.ip, vm.mac, vm.platform_name, vm.product, vm.os, vm.quantityGB, vm.userUsedGB, vm.volProvisionGB])

# 打印表格
print(table.draw())
