#!/bin/bash
set -e

:<<COMMENT
放到生产环境/ckv/loonflow/check_vm.sh

不要print其他信息,只print vm状态

- vmi running: 成功
- vm不存在: 失败:vm不存在(请排查原因后重新创建虚机)
- dv不存在: 失败:dv不存在(请排查原因后重新创建虚机)
- 其他： 运行中(vm:Stopped vmi:Pending dv: 100% 80% N/A)(如果执行太久请联系容器云处理)
COMMENT

namespace=$1
vm_name=$2

if [[ -z $vm_name ]]; then
    echo "失败: 请传入虚机名"
    exit 1
fi

function patch_and_delete_dv() {
    kubectl patch vm -n $namespace $vm_name --type merge -p '{"spec": {"dataVolumeTemplates": null}}'   > /dev/null 2>&1 || true > /dev/null 2>&1
    kubectl get pvc -n $namespace --selector caihckv.io/genvm=$vm_name 2>/dev/null | grep -v NAME | awk '{print $1}' | xargs -I {} kubectl patch pvc {}  --type merge -p '{"metadata": {"ownerReferences": null}}'  > /dev/null 2>&1  || true > /dev/null 2>&1
    kubectl get dv -n $namespace --selector caihckv.io/genvm=$vm_name 2>/dev/null | grep -v NAME | awk '{print $1}' | xargs -I {}  kubectl patch dv {}  --type merge -p '{"metadata": {"ownerReferences": null}}'  > /dev/null 2>&1  || true > /dev/null 2>&1
    sleep 5
    kubectl delete dv -n $namespace --selector caihckv.io/genvm=$vm_name  > /dev/null 2>&1 || true > /dev/null 2>&1
}


if kubectl get vm -n $namespace $vm_name >/dev/null 2>&1 ; then
    vm_status=$(kubectl get vm -n $namespace $vm_name -o jsonpath="{.status.printableStatus}" 2>/dev/nul || echo N/A)
    vmi_status=$(kubectl get vmi -n $namespace $vm_name -o jsonpath="{.status.phase}" 2>/dev/nul || echo N/A)
    if [ "$vmi_status" = "Running" ]; then
        echo "成功: 虚机已开机,虚机名:$namespace/$vm_name"
        # 删除级联关系及dv,忽略错误
        # if kubectl get dv -n $namespace --selector caihckv.io/genvm=$vm_name >/dev/null 2>&1 ; then
        #     patch_and_delete_dv
        # fi
        exit 0
    fi
    # dv不存在返回失败
    dv_num=$(kubectl get dv -n $namespace -l caihckv.io/genvm=$vm_name -o json | jq '.items | length' 2>/dev/nul)
    if [ $dv_num == "0" ]; then
        echo "失败: dv不存在或未创建(请排查原因后点重新创建虚机)"
        exit 0
    fi

    # dv 全部100%算成功
    get_dv_allok=$(kubectl get dv -n $namespace --selector caihckv.io/genvm=$vm_name -o json | jq -e '.items[] | select(.status.phase != "Succeeded")' >/dev/null && echo "Failure" || echo "ok")
    if [ $get_dv_allok == "ok" ]; then
        echo "成功: 请到容器云虚机平台检查虚机,虚机名: $namespace/$vm_name"
        # 删除级联关系及dv,忽略错误
        # if kubectl get dv -n $namespace --selector caihckv.io/genvm=$vm_name >/dev/null 2>&1 ; then
        #     patch_and_delete_dv
        # fi
        exit 0
    fi   
    dv_progress=$(kubectl get dv -n $namespace --selector caihckv.io/genvm=$vm_name -o jsonpath="{.items[*].status.progress}" 2>/dev/nul)
    dv_status=$(kubectl get dv -n $namespace --selector caihckv.io/genvm=$vm_name -o jsonpath="{.items[*].status.phase}" 2>/dev/nul)
    if [[ $dv_status == *"Failed"* ]]; then
        echo "失败:dv异常(请排查原因后点重新创建虚机)"
        exit 0
    fi

else
    echo "失败:vm不存在(请排查原因后点重新创建虚机)"
    exit 0
fi

echo "进行中:(namespace: %namespace vm: $vm_status vmi: $vmi_status dv: $dv_status progress: $dv_progress )(如果执行太久请联系容器云处理)"
