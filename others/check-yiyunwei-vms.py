#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

import requests
import json
from requests.packages.urllib3.exceptions import InsecureRequestWarning


requests.packages.urllib3.disable_warnings(InsecureRequestWarning)  # 屏蔽访问过程中的warning信息

'''
@ 遍历生产测试环境所有虚机，如果虚机不在易运维上或信息有误，则打印出来
之前脚本是为了检查虚机信息不一致问题，现在不一致问题基本已解决，主要为了查询自建虚机或遗留释放的虚机

复制生产/测试虚机名列表放在当前目录下的./ckv_vm_pro和./ckv_vm_test, 一行一个虚机名


此脚本只做检查，检查到异常的虚机, 请手动处理

问题：
- 此脚本查找虚机名是模糊查找，可能查找到多个相同虚机名
- 易运维主机名对应虚机的hostname,可能于平台虚机名不一致，为规避这个问题，已在备注中增加实际虚机名
- 虚机查找失败/非使用中：自建虚机不在易运维，虚机非使用中状态


'''
yyw_url = 'http://**********'
username = "fangfenghuang"
password = "****"



file = open('./ckv_vm_pro', 'r') 
ckv_vm_pro = file.readlines()  
file.close() 

file = open('./ckv_vm_test', 'r') 
ckv_vm_test = file.readlines()
file.close()

all_vm_list = []
all_vm_list = ckv_vm_pro + ckv_vm_test

session = requests.session()
session.headers['Content-Type'] = 'application/json'
res = session.post(f'{yyw_url}/token/', data=json.dumps({"username": username, "password": password}))

try:
    if res.status_code != 200:
        print("认证失败：{}".format(res.status_code))
        exit(1)
    token = res.json()['access']
except Exception as e:
    print(str(e))
    print('认证异常...')
    exit(1)  
session.headers['Authorization'] = f'Bearer {token}'


ckv_vm_err_list = []
for vm_name in all_vm_list:
    if vm_name.strip() == "":
        continue
    old_vm_id = ""
    old_pool = ""
    old_cloud_platform = 9999999
    new_pool = "caihcloud"
    new_cloud_platform = 15  # 容器云虚拟化(测试)-南宁电信
    if vm_name in ckv_vm_pro:
        new_pool = "caihcloudpro"
        new_cloud_platform = 93  # 容器云虚拟化(生产)-南宁电信
    elif vm_name in ckv_vm_test:
        new_pool = "caihcloud"
        new_cloud_platform = 15  # 容器云虚拟化(测试)-南宁电信
    else:
        print(f'[异常] {vm_name} 不匹配')
        continue

    vm_name = vm_name.strip()  # 去除空格
    new_vm_id = "default/"+vm_name.replace("_", "-").lower()

    # 查询易运维虚机信息
    get_vm_res = session.get(f'{yyw_url}/api/hosts/?status=1&&search={vm_name}')
    results = []
    

    for host in get_vm_res.json()['results']:
        # 可能存在查找到多个的情况
        # 现在平台填写基本都是对的，过滤掉平台不一致的情况
        if  host.get('cloud_platform') == None:
            continue
        elif new_cloud_platform != host['cloud_platform']['id']: 
            continue
        # 如果虚机名完全一致，则直接赋值
        if host["hostname"].replace("_", "-").lower() == vm_name:  # 转小写 中横线
            results = [host]
            break

        results.append(host)

    if len(results) == 0:
        print(f'[虚机查找失败/非使用中] [{new_pool}]{vm_name}!!!!!!!')
        continue
    elif len(results) > 1:
        print(f'[找到多个虚机] [{new_pool}]{vm_name}!!!!!!!')
        continue

    host= results[0]
    host_id = host["id"]
    for _attr in host['attribute_value']:
        if _attr['attr']['attr_name'] == 'VM_ID':
            old_vm_id = _attr["stringvalue"]
        if _attr['attr']['attr_name'] == 'POOL':
            old_pool = _attr["stringvalue"]
    if host['cloud_platform'] == None:
        old_cloud_platform = "空"
    else:
        old_cloud_platform = host['cloud_platform']['id']
    yiyunwei_patch_vm_url = f'http://aioms.caih.local/host-info-tabs/{host_id}?asset_type=Virtual%20machine&edit=true'
    if old_vm_id != new_vm_id or old_pool != new_pool or old_cloud_platform != new_cloud_platform:
        print(f'[请手动修改虚机] {vm_name}: ({old_pool} {old_cloud_platform} {old_vm_id}) >> ({new_pool} {new_cloud_platform} {new_vm_id} )  详情见: {yiyunwei_patch_vm_url}')
