#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

'''
fusion虚机迁移-导出查询
调用fusion任务查询接口

'''
from workflow.service.ticket.ticket_base_service import TicketBaseService
import paramiko
import json
import requests
import urllib3
import time

# 任务状态：
## 成功
## 进行中
## 失败

# ERP账号密码
yiyunwei_url = "http://aioms.caih.local"
username = "******"
password = "******"
# ckv节点dapp密码
ckv_node = "" 
ckv_node_password = "******"
# 中转nfs虚机root密码
trans_vm = ""      # 从工单获取
trans_vm_password = "******"
# 华为云用户密码
fusion_addr = ""   # 从工单获取  https://10.10.3.62:8443/ 或https://10.10.31.52:8443/
fusion_authuser = "vmexport"
fusion_authkey = "******"


fusion_platform = {
    "https://10.10.3.62:8443/": {    # 南宁      
        "flag": "4AF909D7",
        "platform_name": "华为云-南宁电信2",
        "pool": "telecom2"
    },
    "https://10.10.31.52:8443/": {  # 北京
        "flag": "380C0718",
        "platform_name": "华为云-北京",
        "pool": "vianet"
    }
}

# def getQueryValue(query):
#     base_url = 'http://************:49090/pms/api/v1/query?query='
#     inquire = base_url + query
#     response = requests.request('GET', inquire, timeout=10)
#     if response.status_code == 200:
#         result = response.json()['data']['result']
#         return result
#     else:
#         print("promethues查询失败: ".format(response.json()))
#         return None

def check():
    # 获取工单信息ip地址信息
    # ticket_id会通过exec传过来
    # 你也可以获取工单的其他字段信息，包括自定义字段的值。根据获取的值做后续处理
    try:
        #resp_threshold, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'resp_threshold')
        trans_vm, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'trans_vm')
        vm_list, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'delvm_list')
        taskid, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'taskid')
        fusion_addr, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'fusion_addr')
        if fusion_addr not in fusion_platform.keys():
            print(f"[失败]请检查华为云地址{fusion_addr}是否正确")
            TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: fusion_platform invalid"})
            return
        fusion_flag = fusion_platform[fusion_addr].get("flag")

        vm_info = json.loads(vm_list)
        for n in vm_info:
            vm_ip = n.get('ip')
            hostname = n.get('hostname').replace('_', '-').lower()

        # 如果任务不存在，且ovf文件存在，则直接返回成功
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname=trans_vm, username='root', port=22, password=trans_vm_password, timeout=10)

        # 如果ovf已存在则直接返回成功
        cmdStr = "ls /app/nfs/{}/{}.ovf".format(vm_ip, vm_ip)
        _, stdout, _ = ssh.exec_command(cmdStr)
        returncode = stdout.channel.recv_exit_status()
        if returncode == 0:
            print("ovf已存在, 跳过, 如需重新导出请删除目录")
            TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"成功: ovf已存在, 跳过,如需重新导出请删除目录"})
            return
        elif taskid == None or taskid == '':
            print("taskid为空且ovf不存在,请检查导出任务是否成功下发")
            TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: taskid为空且ovf不存在,请检查导出任务是否成功下发"})
            return

        urllib3.disable_warnings()
        session = requests.session()
        # 登录
        url =f"{fusion_addr}service/login/form"
        data = {"acceptLanguage": "zh-CN",
        "authKey": fusion_authkey,
        "authType": "0",
        "authUser": fusion_authuser,
        "userType": "0",
        "verification": ""}
        session.headers["Content-Type"]="application/json"
        res = session.post(url, data=json.dumps(data), verify=False, timeout=10)
        session.headers['CSRF-HW'] = res.json()['csrfToken']

        # 查询6h
        for i in range(1720):
            url = f"{fusion_addr}service/sites/{fusion_flag}/tasks/{taskid}"
            res = session.get(url, verify=False)
            if res.status_code == 200:
                status = res.json().get('status')
                # waiting  任务未开始
                # running   任务进行中
                # success  任务执行成功
                # failed  任务执行失败
                # cancelling 任务取消中
                if status == "success":
                    print(res.json())
                    spendTime = (int(res.json().get('finishTime')) - int(res.json().get('startTime')))/int(60)/int(1000)
                    TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"成功: {}min".format(round(spendTime, 2))})
                    return
                elif status == "waiting" or status == "running":
                    progress = res.json().get('progress')
                    TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"进行中: {}".format(progress)})
                else:
                    print(res.json())
                    TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: {}".format(status)})
                    return
            else:
                print("接口调用失败,请检查任务id")
                TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: 接口调用失败"})
                return
            # 查询存储时延指标，如果超过阈值则调用终止任务接口终止镜像导出，避免影响生产
            # storage_fs_pool_latency = getQueryValue('avg_over_time(storage_fs_pool_latency[3m]) >= {}'.format(resp_threshold))
            # storage_os_pool_IO_resp_read = getQueryValue('avg_over_time(storage_os_pool_IO_resp_read[3m]) >= {}'.format(resp_threshold))
            # storage_os_pool_IO_resp_write = getQueryValue('avg_over_time(storage_os_pool_IO_resp_write[3m]) >= {}'.format(resp_threshold))
            # if len(storage_fs_pool_latency) + len(storage_os_pool_IO_resp_read) + len(storage_os_pool_IO_resp_write) > 0:
            #     print(storage_fs_pool_latency)
            #     print(storage_os_pool_IO_resp_read)
            #     print(storage_os_pool_IO_resp_write)
            #     print("存储延迟持续3min内超过{}ms, 将终止导出任务！".format(resp_threshold))
            #     # 发送取消任务的请求
            #     url = "https://10.10.3.62:8443/service/sites/4AF909D7/tasks/{}/cancel".format(taskid)
            #     res = session.post(url, verify=False, timeout=10)
            #     if res.status_code == 200 and res.json() == None: 
            #         # 200也有可能取消任务失败，如果取消成功，返回body为none
            #         TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: {}".format("任务取消")})
            #         return
            #     print(res.json())
            #     print("取消任务失败! ")
            if i == 1719:
                print("6h超时,请界面上确认导出完成后重新执行导出")
                return
            time.sleep(30)
    except Exception as e:
        print("执行异常: ", str(e))
        # traceback.print_exc()
        TicketBaseService.update_ticket_field_value(ticket_id, {"export_status":"失败: 执行异常"})
    finally:
        ssh.close()
        session.close()

check()