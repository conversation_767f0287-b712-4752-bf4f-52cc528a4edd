#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

'''
fusion虚机迁移-镜像查询
查询中转虚机中的result.json文件获取状态

'''
from workflow.service.ticket.ticket_base_service import TicketBaseService
import paramiko
import json
import time

# 任务状态：
## 成功
## 进行中
## 失败

trans_vm_password = 'XXXXXXXXX'

IMAGE_STATUS={
    "processing": "进行中",
    "convert_err": "转换失败",
    "compress_err": "压缩失败",
    "error": "失败",
    "ok": "成功"
}


def check_image():
    # 获取工单信息ip地址信息
    # ticket_id会通过exec传过来
    # 你也可以获取工单的其他字段信息，包括自定义字段的值。根据获取的值做后续处理

    trans_vm, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'trans_vm')
    vm_list, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'delvm_list')
    vm_info = json.loads(vm_list)
    for n in vm_info:
        vm_ip = n.get('ip')
        hostname = n.get('hostname').replace('_', '-').lower()
   
    '''
    - result.json文件不存在，退出报错
    - result.json status：processing/convert_err/compress_err/error/ok
    '''
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname=trans_vm, username='root', port=22, password=trans_vm_password, timeout=10)
        # 检查中转虚机中的result.json
        # 查询6h
        time.sleep(5)
        for i in range(1720):
            cmdStr = "cat /app/nfs/{}/result.json".format(vm_ip)
            _, stdout, _ = ssh.exec_command(cmdStr)
            returncode = stdout.channel.recv_exit_status()
            if returncode != 0:
                print("中转机{}上查询文件失败: {}".format(trans_vm, cmdStr))
                converStr = "nohup python /root/tengyun/convert.py /app/nfs/{} >  /app/nfs/{}/out.log 2>&1 &".format(vm_ip,vm_ip)
                print("请点击重新执行转换，或在中转机{}上手动执行{} 后点击重新执行转换!".format(trans_vm, converStr))
                TicketBaseService.update_ticket_field_value(ticket_id, {"image_status":"失败"})
                return
            dataStr = stdout.read().decode()
            resutlt = json.loads(dataStr)
            # 更新工单字段
            if resutlt.get("status") == None:
                print("parse status invalid!")
                TicketBaseService.update_ticket_field_value(ticket_id, {"image_status":"失败"})
                return
            # 直接拿result.json中的status
            status=IMAGE_STATUS[resutlt["status"]]
            # 获取镜像大小
            try:
                cmdStr = "ls -lh /app/nfs/{}/ | grep -E 'qcow2$|vhd$' | awk '{{printf \"%s  %s;  \", $9, $5}} END {{printf \"\\n\"}}'".format(vm_ip)
                _, stdout, _ = ssh.exec_command(cmdStr)
                returncode = stdout.channel.recv_exit_status()
                if returncode == 0:
                        image_info = stdout.read().decode()
                        TicketBaseService.update_ticket_field_value(ticket_id, {"image_info":image_info})
            except Exception as e:
                print("查询镜像文件失败, 跳过: ", str(e))

            TicketBaseService.update_ticket_field_value(ticket_id, {"image_status":status})

            if "成功" in status:
                total_elapsed_time_formatted=resutlt.get("total_elapsed_time_formatted")
                if resutlt.get("total_elapsed_time_formatted") == None:
                    icketBaseService.update_ticket_field_value(ticket_id, {"image_status":"成功"})
                else:
                    TicketBaseService.update_ticket_field_value(ticket_id, {"image_status":"成功: {}".format(total_elapsed_time_formatted)})
                print("镜像转换完成")
                return
            elif "失败" in status:
                print("镜像转换失败:", resutlt.get("error"))
                return
            if i == 1719:
                print("6h超时,请重新执行检查")
                return
            time.sleep(30)
    except Exception as e:
        print("执行异常: ", str(e))
        # traceback.print_exc()
        TicketBaseService.update_ticket_field_value(ticket_id, {"image_status":"失败"})
    finally:
        ssh.close()

check_image()
