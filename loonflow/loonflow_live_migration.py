#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

'''
fusion虚机迁移-热迁移关机

'''

from workflow.service.ticket.ticket_base_service import TicketBaseService
from requests.packages.urllib3.exceptions import InsecureRequestWarning
import json
import requests
import time

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)  # 屏蔽访问过程中的warning信息


# 华为云用户密码
fusion_addr = ""   # 从工单获取  https://10.10.3.62:8443/ 或https://10.10.31.52:8443/
fusion_authuser = "vmexport"
fusion_authkey = "XXXXXXXX"

fusion_platform = {
    "https://10.10.3.62:8443/": {    # 南宁      
        "flag": "4AF909D7",
        "platform_name": "华为云-南宁电信2",
        "pool": "telecom2"
    },
    "https://10.10.31.52:8443/": {  # 北京
        "flag": "380C0718",
        "platform_name": "华为云-北京",
        "pool": "vianet"
    }
}


fusion_flag = "999999"

def check_task_status(session, taskid, retry, timeout):
    global fusion_flag
    print(f"开始检查任务 {taskid} 的状态...")
    url = f"{fusion_addr}service/sites/{fusion_flag}/tasks/{taskid}"
    for attempt in range(retry):  # 最多尝试5次
        try:
            res = session.get(url, verify=False)  # 启用 SSL 证书验证
            res.raise_for_status()  # 检查请求是否成功
        except requests.exceptions.RequestException as e:
            print(f"接口调用失败, 请检查任务id: {e}")
            return False
        data = res.json()
        status = data.get('status')
        if status == "success":
            return True
        elif status in ["waiting", "running"]:
            progress = data.get('progress')
            print(f"处理任务中，当前进度: {progress}，等待 {timeout} 秒后重试...")
            time.sleep(timeout)  # 等待 30 秒再继续循环
        else:
            print(data)
            return False  # 其他状态则退出循环
    print("已达到最大尝试次数，脚本失败，请手动查看。")
    return False  # 如果达到最大尝试次数，返回 None


def safe_stop(session, vm_id):
    global fusion_flag
    url = f"{fusion_addr}service/sites/{fusion_flag}/vms/{vm_id}/"
    res = session.get(url, verify=False, timeout=10)
    if res.status_code != 200:
        print("查询虚机集群信息接口调用失败")
        return False
    elif res.json().get('errorDes') is not None:
        print("查询虚机集群信息接口报错: ", res.json().get('errorDes'))
        return False
    else:
        if res.json().get('status') != "stopped":
            url = f"{fusion_addr}service/sites/{fusion_flag}/vms/{vm_id}/action/stop"
            data = {"mode": "safe"}
            res = session.post(url, data=json.dumps(data), verify=False)
            if res.status_code != 200:
                print("接口调用失败")
                return False
            elif res.json().get('errorDes') is not None:
                print("接口报错: ", res.json().get('errorDes'))
                return False
            else:
                taskUri = res.json().get('taskUri')
                if taskUri is None:
                    print("失败: get taskUri invalid")
                    return False
                taskid = taskUri.split("tasks/")[-1]  # "/service/sites/4AF909D7/tasks/1931915"
                if taskid:
                    print(f"关闭机器任务，taskid 的值是: {taskid}")
                else:
                    print("taskid为空，关机失败，请手动检查虚拟机状态")
                    return False
                for attempt in range(10):
                    if check_task_status(session, taskid, 5, 30) is True:
                        return True
        else:
            print("没有发送关闭虚机的请求，请手动检查虚拟机状态")
            return True


def live_migration(session, vm_id, hostname, cluster):
    global fusion_flag
    url = f"{fusion_addr}service/sites/{fusion_flag}/vms/{vm_id}/"
    res = session.get(url, verify=False, timeout=10)
    if res.status_code != 200:
        print("查询虚机集群信息接口调用失败")
        return False
    elif res.json().get('errorDes') is not None:
        print("查询虚机集群信息接口报错: ", res.json().get('errorDes'))
        return False
    else:
        if res.json().get('status') != "running":
            return False
        else:
            url = f"{fusion_addr}service/sites/{fusion_flag}/vms/{vm_id}/action/satisfyHosts"
            res = session.get(url, verify=False, timeout=10)
            if res.status_code != 200:
                print("查询可热迁移接口调用失败")
                return False
            else:
                nodelist = json.loads(res.text)
                nodes_in_cluster = [host for host in nodelist if host["clusterName"] == cluster]
                if len(nodes_in_cluster) == 0:
                    print("该集群下没有可迁移的节点")
                    return False
                node = nodes_in_cluster[0]
                data = {
                    "location": node["urn"],
                    "hostName": node["name"],
                    "nodeInfo": {
                        "id": node["urn"],
                        "label": node["name"],
                        "pId": node["clusterUrn"],
                        "ip": node["ip"],
                        "clusterName": node["clusterName"],
                        "iconClass": "icon-fc-newicon25 c-blue1",
                        "isChecked": True
                    },
                    "theMirgateVmsId": [
                        {
                            "instanceId": vm_id,
                            "location": node["urn"],
                            "isHostAndStorageMigrate": False
                        }
                    ],
                    "responseData": {
                        vm_id: {
                            "canMigrate": True,
                            "vmInstanceld": vm_id,
                            "vmName": hostname,
                            "vmNotMigrateHostInfos": []
                        }
                    },
                    "isBindingHost": False,
                    "isBatchMigrate": False,
                    "migrateVmTimeOut": "60",
                    "isFrequencyReduce": False
                }
                url = f"{fusion_addr}service/sites/{fusion_flag}/vms/{vm_id}/action/migrate"
                res = session.post(url, data=json.dumps(data), verify=False)
                if res.status_code != 200:
                    print("接口调用失败")
                    return False
                elif res.json().get('errorDes') is not None:
                    print("接口报错: ", res.json().get('errorDes'))
                    return False
                else:
                    taskUri = res.json().get('taskUri')
                    if taskUri is None:
                        print("失败: get taskUri invalid")
                        return False
                    taskid = taskUri.split("tasks/")[-1]  # "/service/sites/4AF909D7/tasks/1931915"
                    for attempt in range(10):
                        if check_task_status(session, taskid, 20, 60) is True:
                            return True


def run():
    vm_id = ''
    hostname = ''
    global fusion_addr
    vm_list, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'delvm_list')
    migration_cluster, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'migration_cluster')
    is_stop, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'is_stop')
    fusion_addr, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'fusion_addr')
    if fusion_addr not in fusion_platform.keys():
        print(f"[失败]请检查华为云地址{fusion_addr}是否正确")
        return
    global fusion_flag
    fusion_flag = fusion_platform[fusion_addr].get("flag")

    vm_info = json.loads(vm_list)
    for n in vm_info:
        hostname = n.get('hostname').replace('_', '-').lower()
        vm_id = n.get('vm_id')
        for item in n.get('attribute_value'):
            if item.get('attr').get('attr_name') == 'VM_ID':
                vm_id = item.get('stringvalue')
    if vm_id == None or vm_id == '':
        print("get vm_id failed")
        return
    print(f'正在进行{hostname}热迁移到{migration_cluster}集群' + "并关机" if is_stop == "is_stop" else "(不关机)")

    session = requests.session()

    try:
        url = f"{fusion_addr}service/login/form"
        data = {"acceptLanguage": "zh-CN",
                "authKey": fusion_authkey,
                "authType": "0",
                "authUser": fusion_authuser,
                "userType": "0",
                "verification": ""}
        session.headers["Content-Type"] = "application/json"
        res = session.post(url, data=json.dumps(data), verify=False, timeout=10)
        if res.status_code != 200:
            print("华为云认证接口调用失败")
            return False
        elif res.json().get('errorDes') is not None:
            print("华为云认证接口报错: ", res.json().get('errorDes'))
            return False
        session.headers['CSRF-HW'] = res.json()['csrfToken']

        # 查询虚机信息
        url = f"{fusion_addr}/service/sites/{fusion_flag}/vms/{vm_id}/"
        res = session.get(url, verify=False, timeout=10)
        if res.status_code != 200:
            print("查询虚机集群信息接口调用失败")
            return
        elif res.json().get('errorDes') is not None:
            print("查询虚机集群信息接口报错: ", res.json().get('errorDes'))
            return
        else:
            # if res.json().get('name') != hostname:
            #     print("虚机名称对应失败")
            #     return
            clusterName = res.json().get('clusterName')
            if clusterName == migration_cluster:
                # 判断是否在migration_cluster上
                print(f"无需迁移，已处于迁移集群{migration_cluster}中")
                if res.json().get('status') != "stopped" and is_stop == 'is_stop':
                    print("开始关机")
                    if safe_stop(session, vm_id) is not True:
                        print("停止虚机失败")
                        return
                    print("关机成功")
                elif res.json().get('status') == "stopped":
                    # 处于迁移集群中，并为关机状态。执行成功
                    print("已是关机状态")
                    return
                else:
                    # 处于迁移集群中，并为关机状态。执行成功
                    print("无需关机")
                    return
            else:
                # 热迁移至migration_cluster
                print("开始热迁移")
                if live_migration(session, vm_id, hostname, migration_cluster) is not True:
                    print("热迁移失败")
                    return
                print("热迁移完成，60s后继续操作")
                time.sleep(60)
                if is_stop == 'is_stop':
                    print("开始关机")
                    if safe_stop(session, vm_id) is not True:
                        print("停止虚机失败")
                        return
                    print("关机成功")
                return
    except Exception as e:
        print("执行异常: ", str(e))
    finally:
        session.close()


run()
