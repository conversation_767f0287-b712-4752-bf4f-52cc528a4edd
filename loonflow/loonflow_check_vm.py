#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

'''
fusion虚机迁移-虚机查询
远程执行check_vm脚本

'''

from workflow.service.ticket.ticket_base_service import TicketBaseService
import paramiko
import json
import time

# 任务状态：
## 成功
## 进行中
## 失败


# ERP账号密码
yiyunwei_url = "http://aioms.caih.local"
username = "******"
password = "******"
# ckv节点dapp密码
ckv_node = ""   # 从工单获取
ckv_node_password = "******"
# 中转nfs虚机root密码
trans_vm = ""      # 从工单获取
trans_vm_password = "******"
# 华为云用户密码
fusion_addr = ""   # 从工单获取  https://**********:8443/ 或https://***********:8443/
fusion_authuser = "vmexport"
fusion_authkey = "******"


def check_vm():
    # 获取工单信息ip地址信息
    # ticket_id会通过exec传过来
    # 你也可以获取工单的其他字段信息，包括自定义字段的值。根据获取的值做后续处理

    vm_list, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'delvm_list')
    start_time, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'start_time')
    is_start, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'is_start')
    vm_group, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'vm_group')
    ckv_node, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'ckv_node')
    vm_info = json.loads(vm_list)
    vmname = "aaaaaaaaa"
    for n in vm_info:
        #vm_ip = n.get('ip')
        vmname = n.get('hostname').replace('_', '-').lower()

    # 如果ckv_node带-，则ckv_node取-前面的字符串，命名空间取-后面的字符串
    # 如果ckv_node不带-，则ckv_node取全部字符串，命名空间取default
    namespace = "default"
    if '-' in ckv_node:
        namespace = ckv_node.split('-')[1]
        ckv_node = ckv_node.split('-')[0]

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        # 检查生产环境中虚机是否创建
        ssh.connect(hostname=ckv_node, username='dapp', port=22, password=ckv_node_password, timeout=10)
        cmdStr = f"sudo sh /ckv/loonflow/check_vm.sh {namespace} {vmname}"
        time.sleep(30)
        # 查询6h
        for i in range(1720):
            _, stdout, stderr = ssh.exec_command(cmdStr)
            returncode = stdout.channel.recv_exit_status()
            if returncode != 0:
                print("check vm error: ", stderr.read())
                TicketBaseService.update_ticket_field_value(ticket_id, {"vm_status":"失败: 执行异常"})
                return
            status = stdout.read().decode()  # 直接拿到输出作为状态
            TicketBaseService.update_ticket_field_value(ticket_id, {"vm_status":status})
            if "成功" in status:
                end_time = int(time.time())
                TicketBaseService.update_ticket_field_value(ticket_id, {"end_time":end_time})
                total_time = (end_time - start_time)/int(60)
                TicketBaseService.update_ticket_field_value(ticket_id, {"total_time":"{}min".format(round(total_time, 2))})
                # 清理dv,失败只打印
                cmdStr = f"sudo sh /ckv/loonflow/patch_and_delete_dv.sh {namespace} {vmname} {is_start} {vm_group}"
                _, stdout, stderr = ssh.exec_command(cmdStr)
                returncode = stdout.channel.recv_exit_status()
                if returncode != 0:
                    print(cmdStr)
                    print("patch_and_delete_dv error: ", stderr.read())
                else:
                    print(stdout.read().decode())
                return
            elif "失败" in status:
                return
            if i == 1719:
                print("6h超时,请重新执行检查")
                return
            time.sleep(30)
    except Exception as e:
        print("执行异常: ", str(e))
        # traceback.print_exc()
        TicketBaseService.update_ticket_field_value(ticket_id, {"vm_status":"失败: 执行异常"})
    finally:
        ssh.close()

check_vm()

