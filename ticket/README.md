# 工单创建

先在[腾讯文档](https://docs.qq.com/sheet/DZkx0dktpdWtjeWlo?tab=vj0o33)中添加要新建工单的机器信息，然后导出为csv文件，放到`ticket`目录下，文件名为`vm_list.csv`

trans_vm是中转虚机，vm_group 是反亲和标签，vm_network是虚机网络，is_start是是迁移完后是否直接开机,is_stop是热迁移后是否关机，migration_cluster是热迁移目标集群

执行命令

```bash
aioms_username=your_username aioms_password=your_password python create_tickets.py
```

aioms_username 和 aioms_password 是易运维的用户名和密码，如果使用代码里的，请去掉，直接执行

```bash
python create_tickets.py
```
