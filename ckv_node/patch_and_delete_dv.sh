#!/bin/bash
set -e

:<<COMMENT
放到生产环境/ckv/loonflow/patch_and_delete_dv.sh

清理dv脚本
如果失败则停止，但不影响工单运行结果

示例：
sh /ckv/loonflow/patch_and_delete_dv.sh vmname is_start vmgpr_1

- 如果没有vmgroup名, 则不添加到虚拟机分组
COMMENT

namespace=$1
vm_name=$2
is_start=$3
vm_group=$4

if [[ -z $vm_name ]]; then
    echo "失败: 请传入虚机名"
    exit 1
fi


if [ "$is_start" != "is_start" ] && [ "$is_start" != "not_start" ]; then
    echo "失败: 虚机启动参数非法: is_start:启动,not_start:不启动"
    exit 1
fi




kubectl patch vm -n $namespace $vm_name --type merge -p '{"spec": {"dataVolumeTemplates": null}}'
sleep 5
kubectl get pvc -n $namespace --selector caihckv.io/genvm=$vm_name | grep -v NAME | awk '{print $1}' | xargs -I {} kubectl patch pvc {} -n $namespace --type merge -p '{"metadata": {"ownerReferences": null}}'
sleep 5
kubectl get dv -n $namespace --selector caihckv.io/genvm=$vm_name | grep -v NAME | awk '{print $1}' | xargs -I {}  kubectl patch dv {} -n $namespace --type merge -p '{"metadata": {"ownerReferences": null}}'
sleep 5
kubectl delete dv -n $namespace --selector caihckv.io/genvm=$vm_name

# 如果有虚拟机分组参数，在虚拟机开机前修改修增加亲和性
if [[ -n "$vm_group" ]]; then
    if kubectl get vmgroups -n $namespace $vm_group &> /dev/null; then
        echo "add vm $namespace/$vm_name to vmgroup $vm_group..."
        kubectl patch  vm -n $namespace $vm_name --type merge --patch '{
            "spec": {
                "template": {
                    "spec": {
                        "affinity": {
                            "podAntiAffinity":{
                                "preferredDuringSchedulingIgnoredDuringExecution": [
                                    {
                                        "podAffinityTerm": {
                                            "labelSelector": {
                                                "matchLabels": {
                                                    "vmgroups.caihckv.io": "'$vm_group'"
                                                }
                                            },
                                            "topologyKey": "kubernetes.io/hostname"
                                        },
                                        "weight": 100
                                    }
                                ]
                            }
                        }   
                    }
                }
            }
        }'
        sleep 5
        kubectl patch  vm -n $namespace $vm_name --type merge --patch '{
            "spec": {
                "template": {
                    "metadata": {
                        "labels": {
                            "vmgroups.caihckv.io": "'$vm_group'"
                        }
                    }
                }
            }
        }'
        sleep 5
        kubectl label vm -n $namespace $vm_name vmgroups.caihckv.io=$vm_group --overwrite

        vmgr_exist=$(kubectl get vmgroups -n $namespace $vm_group -o json | jq '.vms | index("'${vm_name}'")!=null')
        if [ "$vmgr_exist" == "false" ]; then
            kubectl patch vmgroups -n $namespace $vm_group --type "json" -p '[{"op":"add","path":"/vms/-","value": '${vm_name}'}]'
        else
            echo "vm $namespace/$vm_name already in vmgroup $vm_group, skip add vmgroup..."
        fi
    else
        echo "vmgroup $namespace/$vm_group not exist, skip add vmgroup..."
    fi
fi


if [ "$is_start" = "is_start" ]; then
    vmi_status=$(kubectl get vmi -n $namespace $vm_name -o jsonpath="{.status.phase}" 2>/dev/nul || echo N/A)
    if [ "$vmi_status" != "Running" ]; then
        echo "start vm..."
        sleep 3
        /app/kubernetes/bin/virtctl start $vm_name -n $namespace
        exit 0
    fi
fi

echo "skip start vm..."
