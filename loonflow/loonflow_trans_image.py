#!/usr/bin/python3.6
# -*- coding:utf-8 -*-

'''
fusion虚机迁移-镜像转换
远程执行镜像转换脚本

'''

from workflow.service.ticket.ticket_base_service import TicketBaseService
import paramiko
import json


trans_vm_password = 'XXXXXXXX'
def trans_image():
    # 获取工单信息ip地址信息
    # ticket_id会通过exec传过来
    # 你也可以获取工单的其他字段信息，包括自定义字段的值。根据获取的值做后续处理
    trans_vm, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'trans_vm')
    vm_list, _ = TicketBaseService.get_ticket_field_value(ticket_id, 'delvm_list')
    vm_info = json.loads(vm_list)
    for n in vm_info:
        vm_ip = n.get('ip')
        # hostname = n.get('hostname').replace('_', '-').lower()

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname=trans_vm, username='root', port=22, password=trans_vm_password, timeout=10)
         # 如果已转换完成，不再转换
        cmdStr = """cat /app/nfs/{}/result.json  | jq '.status'  | grep ok""".format(vm_ip)
        _, stdout, _ = ssh.exec_command(cmdStr)
        returncode = stdout.channel.recv_exit_status()
        if returncode == 0:
            print("镜像已转换完成, 跳过执行!")
            return

        cmdStr = "source /etc/profile && nohup python /ckv/loonflow/convert.py /app/nfs/{} >>  /app/nfs/{}/out.log 2>&1 &".format(vm_ip,vm_ip)
        ssh.exec_command(cmdStr)
        print("开始转换")

        _, stdout, stderr = ssh.exec_command(cmdStr)
        returncode = stdout.channel.recv_exit_status()
        if returncode != 0:
            print("cmd: ", cmdStr)
            print(stderr.read())
            TicketBaseService.update_ticket_field_value(ticket_id, {"image_status":"失败： 执行失败"})
            return
        TicketBaseService.update_ticket_field_value(ticket_id, {"image_status":"进行中: 已执行"})
    except Exception as e:
        print("执行异常: ", str(e))
        # traceback.print_exc()
        TicketBaseService.update_ticket_field_value(ticket_id, {"image_status":"失败： 执行异常"})
    finally:
        ssh.close()

trans_image()
