# coding: utf-8

import xml.etree.ElementTree as ET
import sys
import os
import yaml
import re
import argparse
import socket

def replace_suffix(file_name, new_suffix):
    base_name = os.path.splitext(file_name)[0]
    return base_name + new_suffix

def parse_xml(file_path,vmname,vmns, class_storage,network,os_type,macAddr):
    tree = ET.parse(file_path)
    root = tree.getroot()

    namespaces = {
        'ovf': 'http://schemas.dmtf.org/ovf/envelope/1',
        'hw': 'http://www.huawei.com/schema/ovf',
        'rasd': 'http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/CIM_ResourceAllocationSettingData'
    }

    # Extracting the VirtualSystem name
    virtual_system = root.find('.//ovf:VirtualSystem', namespaces)
    if virtual_system is not None:
        name = virtual_system.find('.//ovf:Name', namespaces).text if virtual_system.find('.//ovf:Name', namespaces) is not None else ''
        # Remove ".ovf" from name and ensure it matches k8s naming requirements
        name = re.sub(r'\.ovf$', '', name).lower()
        name = re.sub(r'[^a-z0-9\-]', '-', name)
    else:
        name = ''

    # Extracting CPU and Memory
    cpu = ''
    memory = ''
    items = virtual_system.findall('.//ovf:Item', namespaces)
    for item in items:
        resource_type = item.find('.//rasd:ResourceType', namespaces).text
        if resource_type == '3':  # CPU
            cpu = int(item.find('.//rasd:VirtualQuantity', namespaces).text)
        elif resource_type == '4':  # Memory
            memory = str(int(item.find('.//rasd:VirtualQuantity', namespaces).text) / 1024) + 'Gi'

    # Extracting Disk information
    disks = []
    disk_refs = {}
    references = root.find('.//ovf:References', namespaces)
    if references is not None:
        for file_ref in references.findall('.//ovf:File', namespaces):
            disk_refs[file_ref.get('{http://schemas.dmtf.org/ovf/envelope/1}id')] = file_ref.get('{http://schemas.dmtf.org/ovf/envelope/1}href')

    disk_section = root.find('.//ovf:DiskSection', namespaces)
    if disk_section is not None:
        for disk in disk_section.findall('.//ovf:Disk', namespaces):
            size = disk.get('{http://schemas.dmtf.org/ovf/envelope/1}capacity') + 'Gi'
            disk_id = disk.get('{http://schemas.dmtf.org/ovf/envelope/1}diskId')
            parent_dir = os.path.basename(os.path.dirname(file_path))
            original_file_name = disk_refs.get(disk_id, '')
            new_file_name = replace_suffix(original_file_name, '.min.qcow2')
            #url = 'http://**********/' +parent_dir+'/'+ new_file_name
            local_ip = socket.gethostbyname(socket.getfqdn(socket.gethostname()))
            url = "http://{}/{}/{}".format(local_ip, parent_dir, new_file_name)
            disks.append({'size': size, 'url': url, 'storageClass': class_storage})

    # Preparing the result
    result = {
        'name': vmname,
        'namespace': vmns,  # 虚机命名空间
        'cpu': cpu,
        'memory': memory,
        'network': network,
        'os': os_type,
        'macAddr': macAddr,
        'disks': disks
    }

    # Determine the output file path
    output_dir = os.path.dirname(file_path)
    output_file = os.path.join(output_dir, 'genvm.yaml')

    # Write the result to the YAML file
    with open(output_file, 'w') as f:
        yaml.dump(result, f, default_flow_style=False, allow_unicode=True)

    # Output the result
    with open(output_file, 'r') as f:
        print f.read()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Parse XML and extract information.')
    parser.add_argument('file_path', help='Path to the XML file')
    parser.add_argument('--network', action='store',  help='Extract network information')
    parser.add_argument('--vmname', action='store',  help='Extract vmname information')
    parser.add_argument('--namespace', action='store',  help='Extract namespace information')
    parser.add_argument('--osType', action='store', help='Extract OS type information')
    parser.add_argument('--classStorage', action='store', help='Extract class storage information')
    parser.add_argument('--macAddr', action='store', help='Extract mac addr information')
    args = parser.parse_args()

    parse_xml(args.file_path,args.vmname,args.namespace,args.classStorage,args.network,args.osType,args.macAddr)
